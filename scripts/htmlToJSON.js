function tableToJson(table) {
  let data = [];
  let headers = [];

  // Get the headers
  for (let i = 1; i < table.rows[1].cells.length; i++) {
    headers[i] = table.rows[1].cells[i].innerText;
  }

  // Go through cells
  for (let i = 2; i < table.rows.length; i++) {
    let tableRow = table.rows[i];
    let rowData = { id: i - 1 };

    for (let j = 1; j < tableRow.cells.length; j++) {
      const name = headers[j];
      if (name === 'Competitor email') {
        rowData[headers[j]] = tableRow.cells[j].innerHTML;
      } else {
        rowData[headers[j]] = tableRow.cells[j].innerText;
      }
    }

    data.push(rowData);
  }

  return data;
}

let table = document.querySelector('.waffle');
let json = tableToJson(table);
json = json.map((obj) => ({
  id: obj.id,
  company_name: obj['Company name'],
  logo_url: `/images/jeff/demo-company/logo/${obj['Company name']}.png`,
  company_category: obj['Category'].replace(/\s*\(Department:.*?\)/, ''),
  email1: obj['Competitor email'],
  propspect_link: obj['Propspect'],
  propspect_image: `/images/jeff/demo-company/propspect/${obj['Company name']}.png`
}));
console.log(json);
