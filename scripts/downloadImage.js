const fs = require('fs');
const axios = require('axios');
const company_data = require('../src/views/NewDemo/data/company_data.json');

const download_image = (url, image_path) =>
  axios({
    url,
    responseType: 'stream',
  }).then(
    (response) =>
      new Promise((resolve, reject) => {
        response.data
          .pipe(fs.createWriteStream(image_path))
          .on('finish', () => resolve())
          .on('error', (e) => reject(e));
      }),
  );

(async () => {
  for (const companyDataKey of company_data) {
    const { id, company_name, logo_url } = companyDataKey;
    await download_image(logo_url, `./company/${company_name}-logo.png`);
    console.log('done', id, company_name);
  }
})();
