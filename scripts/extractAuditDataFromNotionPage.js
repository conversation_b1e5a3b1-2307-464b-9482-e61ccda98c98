/**
 * Formating the companies from the SHEET
 */
const a = `Mam baby
Tradegear
Cedarslink
Kraus
Ty
Heritage Steel
Classic Accessories
Phlur`;

const companies = a.split('\n');

console.log('Finding HTML ELEMENT for these companies', companies);

/**
 * Extracting the Table from companyies
 */
const layoutWrapper = document.querySelector('.notion-page-content');
const matching = (text) => {
  for (const value of companies) {
    if (value.toLowerCase() === text.toLowerCase()) return true;
  }
  return false;
};

const auditTable = {};

layoutWrapper.childNodes.forEach((ele) => {
  if (
    ele.className === 'notion-selectable notion-collection_view-block' &&
    ele.childElementCount === 3 &&
    ele.childNodes[1].childElementCount === 2
  ) {
    const textContent = ele.childNodes[1].textContent.trim().toLowerCase();
    if (matching(textContent)) {
      auditTable[textContent] = ele;
    }
  }
});

console.log('Company Map HTML ELEMENT', auditTable);

/**
 * Extracting data
 */

const companiesAuditData = {};

Object.keys(auditTable).forEach((companyName) => {
  const parentDiv = auditTable[companyName];

  const rowTitle = [];
  const headerRow = parentDiv.querySelector(
    '.notion-table-view-header-row > div',
  );

  headerRow.childNodes.forEach((ele) => {
    const textContent = ele.textContent;
    rowTitle.push(textContent.trim());
  });

  const tableBodyRows = parentDiv.querySelectorAll(
    '.notion-table-view-row > div',
  );

  const audit = [];
  tableBodyRows.forEach((row) => {
    const rowData = {};

    if (row.textContent) {
      row.childNodes.forEach((cell, idx) => {
        const textContent = cell.textContent;
        const key = rowTitle[idx];

        if (key) {
          if (key === 'Benefits') {
            const benefitsWrapper = cell.querySelector(
              ':scope div > div > div > div',
            );
            const benefits = [];
            if (benefitsWrapper) {
              benefitsWrapper.childNodes.forEach((ele) => {
                const textContent = ele.textContent;
                if (textContent) {
                  benefits.push(textContent.trim());
                }
              });
            }
            rowData[key] = benefits;
          } else {
            rowData[key] = textContent.trim();
          }
        }
      });

      audit.push(rowData);
    }
  });

  companiesAuditData[companyName] = audit;
});

console.log('EXTRACTED ALL COMPANY AUDIT DATA:', companiesAuditData);

/**
 * Modified And Filter Audit Data
 */

const characteristicPriorityMap = {
  urgent: 1,
  high: 2,
  medium: 3,
  low: 4,
};

const modifiedAuditData = {};

Object.keys(companiesAuditData).forEach((companyName) => {
  const audit = companiesAuditData[companyName];

  const filterAudit = audit.filter((obj) => {
    const whatsWrong = obj['What\'s wrong'];
    const improvement = obj['Optimisation & Improvements'];
    if (whatsWrong && whatsWrong !== '-' && improvement && improvement !== '_')
      return true;
    else return false;
  });
  
  filterAudit.sort((a, b) => {
    const priorityA = characteristicPriorityMap[a.Priority.toLowerCase()];
    const priorityB = characteristicPriorityMap[b.Priority.toLowerCase()];
    return priorityA - priorityB;
  });

  modifiedAuditData[companyName] = filterAudit;
});

console.log('FILTER AND SORT COMPANY AUDIT DATA:', modifiedAuditData);


