const components = {
  application: [
    {
      title: 'Authentication',
      description:
        'Auth forms used to register and sign users with email-password and OAuth solutions.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/auth-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/auth-snippet--dark.svg',
      href: '/blocks/authentication',
      components: 6,
    },
    {
      title: 'Banners',
      description:
        'Used to display an important message at the top or bottom of the page.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/banners-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/banners-snippet--dark.svg',
      href: '/blocks/banners',
      components: 2,
    },
    {
      title: 'Cards',
      description:
        'Used to display information to users, with button to trigger actions.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/cards-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/cards-snippet--dark.svg',
      href: '/blocks/cards',
      components: 3,
    },
    {
      title: 'Form Layouts',
      description:
        'Pre-built layouts which can be easily modified to fit your usecase.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/form-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/form-snippet--dark.svg',
      href: '/blocks/form-layouts',
      components: 2,
    },
    {
      title: 'Grids',
      description:
        'Grid is a 2-dimensional layout system, meaning it can handle both columns and rows.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/grids-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/grids-snippet--dark.svg',
      href: 'https://mui.com/components/grid/',
      target: '_blank',
    },
    {
      title: 'Lists',
      description: 'Lists are a simple ways to displays related data.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/list-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/list-snippet--dark.svg',
      href: '/blocks/lists',
      components: 3,
    },
    {
      title: 'Notifications',
      description: 'Use notifications to inform a users about specific events.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/notifications-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/notifications-snippet--dark.svg',
      href: '/blocks/notifications',
      components: 4,
    },
    {
      title: 'Popovers',
      description:
        'A Popover can be used to display some content on top of another.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/popover-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/popover-snippet--dark.svg',
      href: '/blocks/popovers',
      components: 4,
    },
    {
      title: 'Page Layouts',
      description:
        'Provides common application layout shells to get your app up and running in no time.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/layout-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/layout-snippet--dark.svg',
      href: '/blocks/page-layouts',
      components: 5,
    },
    {
      title: 'Sidebars',
      description:
        'Used to provide a sticky side navigation to your users while using your application.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/sidebar-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/sidebar-snippet--dark.svg',
      href: '/blocks/sidebars',
      components: 3,
    },
    {
      title: 'Stats',
      description: 'Displays usage related data, statistics or limits.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/statistics-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/statistics-snippet--dark.svg',
      href: '/blocks/app-stats',
      components: 3,
    },
    {
      title: 'Progress Steps',
      description:
        'A progress indicator component communicates to the user the progress of a particular process.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/progress-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/progress-snippet--dark.svg',
      href: '/blocks/progress-steps',
      components: 3,
    },
    {
      title: 'Tables',
      description:
        'Used to display data lists to your users in a clean, tabular format.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/table-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/table-snippet--dark.svg',
      href: '/blocks/tables',
      components: 3,
    },
    {
      title: 'Form Control Elements',
      description:
        'Standard form elements with various styles and layout options.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/control-elements-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/control-elements-snippet--dark.svg',
      href: '/blocks/form-controls',
      components: 4,
    },
    {
      title: 'User Cards',
      description:
        'Used to display information about users and contacts in your app.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/user-cards-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/user-cards-snippet--dark.svg',
      href: '/blocks/user-cards',
      components: 3,
    },
  ],
  marketing: [
    {
      title: 'Blog',
      description:
        'Used to display blog content in a clean and organised layout.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/blog-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/blog-snippet--dark.svg',
      href: '/blocks/blog',
      components: 7,
    },
    {
      title: 'Call to Action',
      description:
        'Used to display a prominent call-to-action that converts leads to customers.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/cta-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/cta-snippet--dark.svg',
      href: '/blocks/cta',
      components: 10,
    },
    {
      title: 'Features',
      description:
        'Displays the awesome features of your product or service in an organised layout.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/features-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/features-snippet--dark.svg',
      href: '/blocks/features',
      components: 18,
    },
    {
      title: 'Heroes',
      description:
        'Used to display the core value proposition of your product or service above the fold.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/hero-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/hero-snippet--dark.svg',
      href: '/blocks/heroes',
      components: 17,
    },
    {
      title: 'Logo Grid',
      description: 'Displays a grid of your customers\' or sponsors\' logo.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/logo-grid-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/logo-grid-snippet--dark.svg',
      href: '/blocks/logo-grid',
      components: 6,
    },
    {
      title: 'Newsletter',
      description:
        'Get your visitors and followers to subscribe or signup to your mailing list.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/newsletter-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/newsletter-snippet--dark.svg',
      href: '/blocks/newsletters',
      components: 3,
    },
    {
      title: 'Pricing',
      description:
        'Used to display the prices of your software, product, or service.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/pricing-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/pricing-snippet--dark.svg',
      href: '/blocks/pricing',
      components: 7,
    },
    {
      title: 'Stats',
      description: 'Displays usage related data, statistics or limits.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/statistics-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/statistics-snippet--dark.svg',
      href: '/blocks/stats',
      components: 6,
    },
    {
      title: 'Team',
      description:
        'Used to showcase your team, give your brand a face and gain customers\' trust.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/team-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/team-snippet--dark.svg',
      href: '/blocks/team',
      components: 6,
    },
    {
      title: 'Testimonials',
      description:
        'Rave about customer testimonials of your services and products.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/testimonials-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/testimonials-snippet--dark.svg',
      href: '/blocks/testimonials',
      components: 7,
    },
  ],
  ecommerce: [
    {
      title: 'Category Showcases',
      description: 'Used to showcase a category of products in your store.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/category-showcases-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/category-showcases-snippet--dark.svg',
      href: '/blocks/category-showcases',
      components: 4,
    },
    {
      title: 'Checkout Pages',
      description:
        'Collect payment information and delivery information to complete orders.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/checkout-pages-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/checkout-pages-snippet--dark.svg',
      href: '/blocks/checkout-pages',
      components: 1,
    },
    {
      title: 'Product Details',
      description:
        'Used to display details of a product with beautiful galleries.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/product-details-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/product-details-snippet--dark.svg',
      href: '/blocks/product-details',
      components: 2,
    },
    {
      title: 'Product Filters',
      description:
        'Common product filter patterns used to narrow down product list.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/product-filters-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/product-filters-snippet--dark.svg',
      href: '/blocks/product-filters',
      components: 2,
    },
    {
      title: 'Product Grids',
      description: 'Great for presenting your products in an organized way.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/product-grids-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/product-grids-snippet--dark.svg',
      href: '/blocks/product-grids',
      components: 3,
    },
    {
      title: 'Product Pickers',
      description:
        'Common eCommerce product controls like color picker, size picker, etc.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/product-pickers-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/product-pickers-snippet--dark.svg',
      href: '/blocks/product-pickers',
      components: 3,
    },
    {
      title: 'Product Quick Views',
      description:
        'Used to quickly explore a product\'s detail within a modal dialog.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/product-quick-views-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/product-quick-views-snippet--dark.svg',
      href: '/blocks/product-quick-views',
      components: 1,
    },
    {
      title: 'Reviews',
      description: 'Used to display the customer reviews for a product.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/product-reviews-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/product-reviews-snippet--dark.svg',
      href: '/blocks/product-reviews',
      components: 3,
    },
    {
      title: 'Shopping Carts',
      description: 'Used to display products added to the store shopping cart.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/shopping-carts-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/shopping-carts-snippet--dark.svg',
      href: '/blocks/shopping-carts',
      components: 2,
    },
    {
      title: 'Store Navigation',
      description: 'Common navigation patterns for your store.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/store-navigation-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/store-navigation-snippet--dark.svg',
      href: '/blocks/store-navigation',
      components: 1,
    },
    {
      title: 'Store Popups',
      description: 'Common marketing popups to incentivize store visitors.',
      snippet:
        'https://assets.maccarianagency.com/svg/snippets/store-popups-snippet.svg',
      snippetDark:
        'https://assets.maccarianagency.com/svg/snippets/store-popups-snippet--dark.svg',
      href: '/blocks/store-popups',
      components: 2,
    },
  ],
};

export default components;
