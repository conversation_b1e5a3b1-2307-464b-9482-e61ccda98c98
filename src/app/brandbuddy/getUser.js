import { API_ENDPOINTS, getBaseURL } from '../../config/api';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

const baseUrl = getBaseURL();

const getUser = async () => {
  const cookieStore = cookies();
  const authorization = cookieStore.get('authorization');
  if (!authorization) {
    return redirect('/brandbuddy/signin');
  }

  const headers = new Headers();
  headers.append('authorization', authorization.value);

  const url = baseUrl + API_ENDPOINTS.CURRENT_USER;

  try {
    const res = await fetch(url, {
      method: 'GET',
      headers,
    });
    const data = await res.json();

    if (data.error) {
      return redirect('/brandbuddy/signin');
    }
    return data;
  } catch (e) {
    return redirect('/brandbuddy/signin');
  }
};

export { getUser };
