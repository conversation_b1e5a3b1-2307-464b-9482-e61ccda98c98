import React from 'react';
import LexLandingPage from 'views/Lex';

const metaDetails = {
  title: 'AI Employee to Remove 1-Star Reviews from Amazon | Equal Collective',
  description:
    '<PERSON> will find the 1-star reviews that are not compliant with Amazon’s Policy. And make a strong case with Amazon on your behalf to remove it. Works on a success fee & 100% Amazon ToS-compliant!',
  image: '/images/lex-og.png',
  url: '/lex',
};

export const metadata = {
  title: metaDetails.title,
  description: metaDetails.description,
  twitter: {
    card: 'summary_large_image',
    title: metaDetails.title,
    images: [metaDetails.image],
  },
  openGraph: {
    title: metaDetails.title,
    url: metaDetails.url,
    siteName: 'Equal Collective',
    images: [
      {
        url: metaDetails.image,
        width: 800,
        height: 600,
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
};

const LexLanding = async () => {
  return <LexLandingPage />;
};

export default LexLanding;
