import React from 'react';
import EcomLandingPage from 'views/EcomConsultingLanding';

const metaDetails = {
  title: 'Custom AI Solutions for eCommerce Businesses | Equal Collective',
  image: '/images/ecom-sellers-og.png',
  url: '/ecom-sellers',
};

export const metadata = {
  title: metaDetails.title,
  twitter: {
    card: 'summary_large_image',
    title: metaDetails.title,
    images: [metaDetails.image],
  },
  openGraph: {
    title: metaDetails.title,
    url: metaDetails.url,
    siteName: 'Equal Collective',
    images: [
      {
        url: metaDetails.image,
        width: 800,
        height: 600,
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
};

const EComLanding = () => {
  return <EcomLandingPage />;
};

export default EComLanding;
