import React from 'react';

import axios from 'axios';
import { API_ENDPOINTS } from 'config/api';

import <PERSON><PERSON><PERSON><PERSON> from 'views/Jeff/JeffAuditPage/JeffAudit';

const JeffAuditPage = async ({ params }) => {
  let data = null;

  try {
    const response = await axios.get(
      `https://api.jeff.equalcollective.com${API_ENDPOINTS.AMAZON_AUDIT_REPORT}/${params.slug}`,
    );

    data = await response.data;
  } catch (error) {
    console.error('Failed to fetch data:>>>>>>>>>>>>>>>>>>>>>', error);
  }

  return <JeffAudit params={params} companyData={data} />;
};

export default JeffAuditPage;
