import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Grid,
  TextField,
  Typography,
  Card,
  CardContent,
  InputAdornment,
} from '@mui/material';
import AddCircleOutlineIcon from '@mui/icons-material/AddCircleOutline';

const CampaignForm = ({ 
  onSubmit, 
  isPending, 
  campaignStats, 
  isDarkMode 
}) => {
  return (
    <Box>
      <Typography 
        variant="h4" 
        mb={2}
        color={isDarkMode ? '#90CAF9' : '#1976d2'}
        sx={{ 
          fontWeight: 600,
          letterSpacing: '0.25px',
          textShadow: isDarkMode ? '0px 1px 2px rgba(0,0,0,0.3)' : 'none',
        }}
      >
        Add New Campaign
      </Typography>
      <Card
        elevation={isDarkMode ? 2 : 1}
        sx={{ 
          borderColor: 'divider',
          backgroundColor: isDarkMode ? '#2D3748' : '#ffffff',
          boxShadow: isDarkMode 
            ? '0px 3px 3px -2px rgba(0,0,0,0.2),0px 3px 4px 0px rgba(0,0,0,0.14),0px 1px 8px 0px rgba(0,0,0,0.12)'
            : '0px 2px 1px -1px rgba(0,0,0,0.1),0px 1px 1px 0px rgba(0,0,0,0.07),0px 1px 3px 0px rgba(0,0,0,0.06)'
        }}
      >
        <CardContent sx={{ color: isDarkMode ? '#E2E8F0' : 'inherit' }}>
          <Box
            component="form"
            onSubmit={onSubmit}
            sx={{ mt: 1 }}
          >
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  autoComplete="campaign-name"
                  name="campaign"
                  required
                  fullWidth
                  id="campaign"
                  label="Campaign Name"
                  placeholder="Enter campaign name"
                  autoFocus
                  size="small"
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <AddCircleOutlineIcon 
                          sx={{ 
                            color: isDarkMode ? '#90CAF9' : '#1976d2',
                          }} 
                        />
                      </InputAdornment>
                    ),
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      color: isDarkMode ? '#e2e8f0' : '#4a5568',
                      '& fieldset': {
                        borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.23)' : 'rgba(0, 0, 0, 0.23)',
                      },
                      '&:hover fieldset': {
                        borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.4)' : 'rgba(0, 0, 0, 0.4)',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                      },
                    },
                    '& .MuiInputLabel-root': {
                      color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)',
                    },
                    '& .MuiInputAdornment-root': {
                      color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.54)',
                    },
                    '& .MuiInputBase-input::placeholder': {
                      color: isDarkMode ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.4)',
                      opacity: 1,
                    },
                  }}
                />
              </Grid>
            </Grid>
            <Grid container spacing={2} sx={{ mt: 3 }}>
              <Grid item xs={6}>
                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  disabled={isPending}
                  sx={{
                    boxShadow: isDarkMode ? '0px 2px 4px rgba(0,0,0,0.3)' : '0px 1px 2px rgba(0,0,0,0.1)',
                    backgroundColor: isDarkMode ? '#3182CE' : '#2f6ad9',
                    color: '#ffffff',
                    '&:hover': {
                      boxShadow: isDarkMode ? '0px 3px 6px rgba(0,0,0,0.4)' : '0px 2px 4px rgba(0,0,0,0.2)',
                      backgroundColor: isDarkMode ? '#4299E1' : '#3b7be8'
                    },
                    transition: 'all 0.2s ease-in-out',
                    fontWeight: 500,
                    textTransform: 'none',
                    borderRadius: '4px'
                  }}
                >
                  {isPending ? 'Creating...' : 'Create Campaign'}
                </Button>
              </Grid>
              <Grid item xs={6}>
                <Box sx={{ 
                  border: '1px solid', 
                  borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.12)' : 'rgba(0, 0, 0, 0.12)', 
                  borderRadius: 1, 
                  p: 1, 
                  height: '100%', 
                  display: 'flex', 
                  alignItems: 'center',
                  justifyContent: 'center',
                  bgcolor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.04)',
                  boxShadow: isDarkMode ? '0px 1px 3px rgba(0, 0, 0, 0.2)' : 'none'
                }}>
                  <Typography 
                    variant="body1" 
                    fontWeight="500"
                    color={isDarkMode ? '#e2e8f0' : '#333333'}
                    sx={{ 
                      '& strong': {
                        color: isDarkMode ? '#90CAF9' : '#1976d2',
                        fontWeight: 600
                      }
                    }}
                  >
                    Total Campaigns: <strong>{campaignStats.total}</strong>
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default CampaignForm; 