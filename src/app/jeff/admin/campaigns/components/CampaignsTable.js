import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
} from '@mui/material';

const CampaignsTable = ({ 
  campaigns, 
  isLoading, 
  searchTerm,
  isDarkMode 
}) => {
  return (
    <Box mt={4}>
      <Typography 
        variant="h4" 
        mb={3}
        color={isDarkMode ? '#90CAF9' : '#1976d2'}
        sx={{ 
          fontWeight: 600,
          letterSpacing: '0.25px',
          textShadow: isDarkMode ? '0px 1px 2px rgba(0,0,0,0.3)' : 'none',
        }}
      >
        All Campaigns
      </Typography>
      {isLoading ? (
        <Box display="flex" justifyContent="center" my={4}>
          <CircularProgress sx={{ color: isDarkMode ? '#90CAF9' : '#1976d2' }} />
        </Box>
      ) : (
        <TableContainer 
          component={Paper} 
          elevation={isDarkMode ? 3 : 1}
          sx={{ 
            borderColor: 'divider',
            backgroundColor: isDarkMode ? '#1E293B' : '#ffffff',
            boxShadow: isDarkMode ? '0px 3px 15px rgba(0,0,0,0.2)' : '0px 2px 8px rgba(0,0,0,0.05)',
            overflow: 'hidden',
            borderRadius: '8px',
          }}
        >
          <Table aria-label="campaigns table">
            <TableHead>
              <TableRow sx={{ 
                bgcolor: isDarkMode ? '#2D3748' : '#f0f7ff',
                '& .MuiTableCell-root': {
                  color: isDarkMode ? '#90CAF9' : '#1976d2',
                  fontWeight: 600,
                  fontSize: '0.875rem',
                  borderBottom: isDarkMode ? '1px solid rgba(255, 255, 255, 0.12)' : '1px solid rgba(0, 0, 0, 0.12)',
                  py: 2
                }
              }}>
                <TableCell>Campaign Name</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {campaigns.length > 0 ? (
                campaigns.map((campaignObj, index) => (
                  <TableRow 
                    key={index}
                    hover
                    sx={{
                      '&:nth-of-type(odd)': {
                        backgroundColor: isDarkMode 
                          ? 'rgba(255, 255, 255, 0.05)' 
                          : 'rgba(0, 0, 0, 0.02)',
                      },
                      '&:hover': {
                        backgroundColor: isDarkMode
                          ? 'rgba(66, 153, 225, 0.08)'
                          : 'rgba(49, 130, 206, 0.04)',
                        cursor: 'pointer',
                      },
                      transition: 'background-color 0.2s',
                      '& .MuiTableCell-root': {
                        borderBottom: isDarkMode 
                          ? '1px solid rgba(255, 255, 255, 0.08)' 
                          : '1px solid rgba(0, 0, 0, 0.08)'
                      }
                    }}
                  >
                    <TableCell>
                      <Typography 
                        variant="body2" 
                        color={isDarkMode ? '#e2e8f0' : '#333333'}
                        fontWeight="medium"
                      >
                        {campaignObj.campaign}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={1} align="center">
                    <Typography
                      variant="body2"
                      color={isDarkMode ? '#e2e8f0' : '#333333'}
                      sx={{ fontStyle: 'italic', py: 2 }}
                    >
                      {searchTerm ? 'No campaigns match your search' : 'No campaigns found'}
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Box>
  );
};

export default CampaignsTable; 