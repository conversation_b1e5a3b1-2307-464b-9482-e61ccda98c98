/**
 * Utility functions for handling form state and operations in the campaign management
 */

// Initial state for the new campaign form
export const initialCampaignForm = {
  name: '',
  description: '',
  clientId: ''
};

/**
 * Filter campaigns based on search term
 * @param {Array} campaigns - Array of campaign objects
 * @param {string} searchTerm - Search term to filter by
 * @returns {Array} Filtered array of campaigns
 */
export const filterCampaigns = (campaigns, searchTerm) => {
  if (!campaigns || !Array.isArray(campaigns)) return [];
  return campaigns.filter(campaign => 
    campaign.campaign.toLowerCase().includes(searchTerm.toLowerCase())
  );
};

/**
 * Calculate campaign statistics
 * @param {Array} campaigns - Array of campaign objects
 * @returns {Object} Object containing campaign stats
 */
export const calculateCampaignStats = (campaigns) => {
  if (!campaigns || !Array.isArray(campaigns)) {
    return {
      total: 0,
      active: 0,
      inactive: 0,
    };
  }
  
  return {
    total: campaigns.length || 0,
    active: campaigns.filter(c => c.status === 'active').length || 0,
    inactive: campaigns.filter(c => c.status !== 'active').length || 0,
  };
}; 