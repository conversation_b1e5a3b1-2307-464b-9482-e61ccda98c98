import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>po<PERSON>,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  Chip,
  IconButton,
  Tooltip,
  TextField,
  InputAdornment,
  useTheme,
} from '@mui/material';
import {
  CloudUpload,
  Download,
  Search as SearchIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

const VisuallyHiddenInput = styled('input')`
  clip: rect(0 0 0 0);
  clip-path: inset(50%);
  height: 1px;
  overflow: hidden;
  position: absolute;
  bottom: 0;
  left: 0;
  white-space: nowrap;
  width: 1px;
`;

const getStatusColor = (status) => {
  switch (status?.toLowerCase()) {
    case 'completed':
      return 'success';
    case 'processing':
      return 'warning';
    case 'queued':
      return 'info';
    case 'cancelled':
      return 'error';
    case 'failed':
      return 'error';
    default:
      return 'default';
  }
};

const truncateFilename = (filename, maxLength = 40) => {
  if (!filename) return '';
  if (filename.length <= maxLength) return filename;
  const extension = filename.split('.').pop();
  const name = filename.substring(0, maxLength - 4);
  return `${name}...${extension}`;
};

const ReviewJobsPanel = ({
  loading,
  error,
  searchTerm,
  setSearchTerm,
  isDarkMode,
  handleFileUpload,
  handleDownload,
  handleDownloadAll,
  handleDownloadAllRemoved,
  fetchJobs,
  filteredJobs,
}) => {
  const theme = useTheme();

  return (
    <>
      <Card
        sx={{
          mb: 3,
          backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        }}
      >
        <CardContent>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mb: 2,
            }}
          >
            <Typography
              variant="h6"
              sx={{
                fontWeight: 600,
                color: isDarkMode ? '#fff' : 'inherit',
              }}
            >
              Upload New Review File
            </Typography>
            <Button
              component="label"
              variant="contained"
              startIcon={<CloudUpload />}
              sx={{
                backgroundColor: isDarkMode ? '#90CAF9' : '#1976d2',
                '&:hover': {
                  backgroundColor: isDarkMode ? '#64B5F6' : '#1565C0',
                },
              }}
            >
              Upload CSV
              <VisuallyHiddenInput
                type="file"
                accept=".csv"
                onChange={handleFileUpload}
              />
            </Button>
          </Box>
          {error && (
            <Alert
              severity="error"
              sx={{
                mb: 2,
                backgroundColor: isDarkMode
                  ? 'rgba(211, 47, 47, 0.1)'
                  : undefined,
                color: isDarkMode ? '#ff5252' : undefined,
                '& .MuiAlert-icon': {
                  color: isDarkMode ? '#ff5252' : undefined,
                },
              }}
            >
              {error}
            </Alert>
          )}
        </CardContent>
      </Card>

      <Card
        sx={{
          backgroundColor: isDarkMode ? '#1a2035' : '#ffffff',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        }}
      >
        <CardContent>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mb: 3,
            }}
          >
            <Typography
              variant="h6"
              sx={{
                fontWeight: 600,
                color: isDarkMode ? '#fff' : 'inherit',
              }}
            >
              Review Jobs
            </Typography>
            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              <Button
                variant="outlined"
                size="small"
                startIcon={<Download />}
                onClick={handleDownloadAll}
                sx={{
                  borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  color: isDarkMode ? '#90CAF9' : '#1976d2',
                  '&:hover': {
                    borderColor: isDarkMode ? '#64B5F6' : '#1565C0',
                    backgroundColor: isDarkMode
                      ? 'rgba(144, 202, 249, 0.08)'
                      : 'rgba(25, 118, 210, 0.08)',
                  },
                }}
              >
                Download All Reviews
              </Button>
              <Button
                variant="outlined"
                size="small"
                startIcon={<Download />}
                onClick={handleDownloadAllRemoved}
                sx={{
                  borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  color: isDarkMode ? '#90CAF9' : '#1976d2',
                  '&:hover': {
                    borderColor: isDarkMode ? '#64B5F6' : '#1565C0',
                    backgroundColor: isDarkMode
                      ? 'rgba(144, 202, 249, 0.08)'
                      : 'rgba(25, 118, 210, 0.08)',
                  },
                }}
              >
                Download Removed Reviews
              </Button>
              <TextField
                size="small"
                placeholder="Search files..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon
                        sx={{
                          color: isDarkMode
                            ? 'rgba(255, 255, 255, 0.7)'
                            : 'inherit',
                        }}
                      />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  width: '250px',
                  '& .MuiOutlinedInput-root': {
                    backgroundColor: isDarkMode
                      ? 'rgba(255, 255, 255, 0.05)'
                      : 'inherit',
                    color: isDarkMode ? '#fff' : 'inherit',
                    '& fieldset': {
                      borderColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.23)'
                        : 'inherit',
                    },
                    '&:hover fieldset': {
                      borderColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.4)'
                        : 'inherit',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: isDarkMode
                        ? '#90CAF9'
                        : theme.palette.primary.main,
                    },
                  },
                  '& .MuiInputBase-input::placeholder': {
                    color: isDarkMode
                      ? 'rgba(255, 255, 255, 0.5)'
                      : 'inherit',
                  },
                }}
              />
              <Tooltip title="Refresh jobs">
                <IconButton
                  onClick={fetchJobs}
                  disabled={loading}
                  sx={{
                    color: isDarkMode
                      ? 'rgba(255, 255, 255, 0.7)'
                      : 'inherit',
                    '&:hover': {
                      backgroundColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.08)'
                        : undefined,
                    },
                  }}
                >
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell
                    sx={{
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.7)'
                        : 'inherit',
                      borderBottomColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.12)'
                        : undefined,
                    }}
                  >
                    Job ID
                  </TableCell>
                  <TableCell
                    sx={{
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.7)'
                        : 'inherit',
                      borderBottomColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.12)'
                        : undefined,
                    }}
                  >
                    Filename
                  </TableCell>
                  <TableCell
                    sx={{
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.7)'
                        : 'inherit',
                      borderBottomColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.12)'
                        : undefined,
                    }}
                  >
                    Status
                  </TableCell>
                  <TableCell
                    sx={{
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.7)'
                        : 'inherit',
                      borderBottomColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.12)'
                        : undefined,
                    }}
                  >
                    Pending Reviews
                  </TableCell>
                  <TableCell
                    sx={{
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.7)'
                        : 'inherit',
                      borderBottomColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.12)'
                        : undefined,
                    }}
                  >
                    Removed Reviews
                  </TableCell>
                  <TableCell
                    sx={{
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.7)'
                        : 'inherit',
                      borderBottomColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.12)'
                        : undefined,
                    }}
                  >
                    Created At
                  </TableCell>
                  <TableCell
                    align="right"
                    sx={{
                      color: isDarkMode
                        ? 'rgba(255, 255, 255, 0.7)'
                        : 'inherit',
                      borderBottomColor: isDarkMode
                        ? 'rgba(255, 255, 255, 0.12)'
                        : undefined,
                    }}
                  >
                    Actions
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell
                      colSpan={7}
                      align="center"
                      sx={{
                        borderBottomColor: isDarkMode
                          ? 'rgba(255, 255, 255, 0.12)'
                          : undefined,
                      }}
                    >
                      <CircularProgress size={24} sx={{ my: 2 }} />
                    </TableCell>
                  </TableRow>
                ) : filteredJobs.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={7}
                      align="center"
                      sx={{
                        borderBottomColor: isDarkMode
                          ? 'rgba(255, 255, 255, 0.12)'
                          : undefined,
                      }}
                    >
                      <Typography
                        variant="body2"
                        sx={{
                          py: 2,
                          color: isDarkMode
                            ? 'rgba(255, 255, 255, 0.5)'
                            : 'text.secondary',
                        }}
                      >
                        {searchTerm
                          ? 'No matching jobs found'
                          : 'No jobs found'}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredJobs.map((job) => (
                    <TableRow key={job.id}>
                      <TableCell
                        sx={{
                          color: isDarkMode ? '#fff' : 'inherit',
                          borderBottomColor: isDarkMode
                            ? 'rgba(255, 255, 255, 0.12)'
                            : undefined,
                        }}
                      >
                        {job.id}
                      </TableCell>
                      <TableCell
                        sx={{
                          color: isDarkMode ? '#fff' : 'inherit',
                          borderBottomColor: isDarkMode
                            ? 'rgba(255, 255, 255, 0.12)'
                            : undefined,
                        }}
                      >
                        <Tooltip title={job.fileName}>
                          <span>{truncateFilename(job.fileName)}</span>
                        </Tooltip>
                      </TableCell>
                      <TableCell
                        sx={{
                          borderBottomColor: isDarkMode
                            ? 'rgba(255, 255, 255, 0.12)'
                            : undefined,
                        }}
                      >
                        <Chip
                          label={job.csvStatus}
                          color={getStatusColor(job.csvStatus)}
                          size="small"
                          sx={{
                            textTransform: 'capitalize',
                            color: isDarkMode ? '#fff' : undefined,
                          }}
                        />
                      </TableCell>
                      <TableCell
                        sx={{
                          color: isDarkMode ? '#fff' : 'inherit',
                          borderBottomColor: isDarkMode
                            ? 'rgba(255, 255, 255, 0.12)'
                            : undefined,
                        }}
                      >
                        {job.pendingReviews} / {job.totalReviews}
                      </TableCell>
                      <TableCell
                        sx={{
                          color: isDarkMode ? '#fff' : 'inherit',
                          borderBottomColor: isDarkMode
                            ? 'rgba(255, 255, 255, 0.12)'
                            : undefined,
                        }}
                      >
                        {job.removedReviews}
                      </TableCell>
                      <TableCell
                        sx={{
                          color: isDarkMode ? '#fff' : 'inherit',
                          borderBottomColor: isDarkMode
                            ? 'rgba(255, 255, 255, 0.12)'
                            : undefined,
                        }}
                      >
                        {new Date(job.createdAt).toLocaleString()}
                      </TableCell>
                      <TableCell
                        align="right"
                        sx={{
                          borderBottomColor: isDarkMode
                            ? 'rgba(255, 255, 255, 0.12)'
                            : undefined,
                        }}
                      >
                        {job.csvStatus === 'COMPLETED' && (
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={<Download />}
                            onClick={() => handleDownload(job.id)}
                            sx={{
                              borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                              color: isDarkMode ? '#90CAF9' : '#1976d2',
                              '&:hover': {
                                borderColor: isDarkMode
                                  ? '#64B5F6'
                                  : '#1565C0',
                                backgroundColor: isDarkMode
                                  ? 'rgba(144, 202, 249, 0.08)'
                                  : 'rgba(25, 118, 210, 0.08)',
                              },
                            }}
                          >
                            Download
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </>
  );
};

export default ReviewJobsPanel; 