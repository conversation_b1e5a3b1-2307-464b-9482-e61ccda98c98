'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Tab,
  Tabs,
} from '@mui/material';
import JeffAdminPanel from 'views/Jeff/JeffAdminPanel';
import { useAdminTheme } from 'views/Jeff/AdminThemeContext';
import { getSellerBotAxiosInstance } from 'config/axios';
import { API_ENDPOINTS } from 'config/api';
import TabPanel from '../leads/components/TabPanel';
import ReviewJobsPanel from './components/ReviewJobsPanel';
import RemovedReviewsPanel from './components/RemovedReviewsPanel';
import Notification from 'components/Notification';


export default function LexReviewSystem() {
  const [jobs, setJobs] = useState([]);
  const [removedReviews, setRemovedReviews] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const { mode: adminThemeMode } = useAdminTheme();
  const isDarkMode = adminThemeMode === 'dark';
  const [tabValue, setTabValue] = useState(0);
  const [notification, setNotification] = useState({
    open: false,
    message: '',
    severity: 'error'
  });

  const [paginationMeta, setPaginationMeta] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const fetchJobs = async () => {
    try {
      setLoading(true);
      const response = await getSellerBotAxiosInstance().get(
        API_ENDPOINTS.SB_LIST_REVIEW_JOBS,
      );
      if (response.data) {
        setJobs(response.data);
      } else {
        throw new Error('No data received from server');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchRemovedReviews = async (page = 1, limit = 10) => {
    try {
      setLoading(true);
      const response = await getSellerBotAxiosInstance().get(
        `${API_ENDPOINTS.LEX_REMOVED_REVIEWS}?page=${page}&limit=${limit}`,
      );
      if (response.data) {
        setRemovedReviews(response.data.data);
        setPaginationMeta(response.data.meta);
      } else {
        throw new Error('No data received from server');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchJobs();
  }, []);

  useEffect(() => {
    fetchRemovedReviews(paginationMeta.page, paginationMeta.limit);
  }, [paginationMeta.page]);

  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const formData = new FormData();
    formData.append('csvFile', file);

    try {
      setLoading(true);
      const response = await getSellerBotAxiosInstance().post(
        API_ENDPOINTS.SB_REVIEW_JOBS,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        },
      );

      if (!response.data) {
        throw new Error('Failed to upload file');
      }

      await fetchJobs();
      setError(null);
    } catch (err) {
      if (err.response?.status === 400) {
        const errorData = err.response.data;
        let errorMessage = errorData.error + '\n';
        if (errorData.details) {
          errorMessage += errorData.details.join('\n');
        }
        if (errorData.invalidRows) {
          errorMessage += '\n\nInvalid rows: ' + errorData.invalidRows.join(', ');
        }
        setNotification({
          open: true,
          message: errorMessage,
          severity: 'error'
        });
        setError(errorMessage);
      } else {
        const errorMsg = err.response?.data?.message || err.message;
        setNotification({
          open: true,
          message: errorMsg,
          severity: 'error'
        });
        setError(errorMsg);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async (jobId) => {
    try {
      const downloadEndpoint = API_ENDPOINTS.LEX_JOB_DOWNLOAD.replace(
        ':jobId',
        jobId,
      );
      const response = await getSellerBotAxiosInstance().get(downloadEndpoint, {
        responseType: 'blob',
      });

      const blob = new Blob([response.data]);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `processed_reviews_${jobId}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      setError(err.response?.data?.message || err.message);
    }
  };

  const handleDownloadAll = async () => {
    try {
      const response = await getSellerBotAxiosInstance().get(
        API_ENDPOINTS.LEX_ALL_JOB_DOWNLOAD,
        {
          responseType: 'blob',
        }
      );

      const blob = new Blob([response.data]);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'all_processed_reviews.csv';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      setError(err.response?.data?.message || err.message);
    }
  };

  const handleDownloadAllRemoved = async () => {
    try {
      const response = await getSellerBotAxiosInstance().get(
        API_ENDPOINTS.LEX_ALL_REMOVED_DOWNLOAD,
        {
          responseType: 'blob',
        },
      );

      const blob = new Blob([response.data]);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'all_removed_reviews.csv';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      setError(err.response?.data?.message || err.message);
    }
  };

  const filteredJobs = jobs.filter((job) =>
    job.fileName.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  const content = (
    <Box>
      <Tabs
        value={tabValue}
        onChange={handleTabChange}
        sx={{ mb: 3 }}
        textColor="primary"
        indicatorColor="primary"
      >
        <Tab label="Review Jobs" />
        <Tab label="Removed Reviews" />
      </Tabs>
      <TabPanel value={tabValue} index={0}>
        <ReviewJobsPanel
          jobs={jobs}
          loading={loading}
          error={error}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          isDarkMode={isDarkMode}
          handleFileUpload={handleFileUpload}
          handleDownload={handleDownload}
          handleDownloadAll={handleDownloadAll}
          handleDownloadAllRemoved = {handleDownloadAllRemoved}
          fetchJobs={fetchJobs}
          filteredJobs={filteredJobs}
        />
      </TabPanel>
      <TabPanel value={tabValue} index={1}>
        <RemovedReviewsPanel
          removedReviews={removedReviews}
          paginationMeta={paginationMeta}
          isDarkMode={isDarkMode}
          fetchRemovedReviews={fetchRemovedReviews}
        />
      </TabPanel>
    </Box>
  );

  const handleCloseNotification = () => {
    setNotification(prev => ({ ...prev, open: false }));
  };

  return (
    <JeffAdminPanel title="Lex Review System">
      {content}
      <Notification
        open={notification.open}
        handleClose={handleCloseNotification}
        message={notification.message}
        severity={notification.severity}
      />
    </JeffAdminPanel>
  );
}
