'use client';

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Container,
  Tabs,
  Tab,
  Paper,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  CircularProgress,
} from '@mui/material';
import JeffAdminPanel from 'views/Jeff/JeffAdminPanel';
import { useAdminTheme } from 'views/Jeff/AdminThemeContext';
import { getSellerBotAxiosInstance } from 'config/axios';
import { API_ENDPOINTS } from 'config/api';
import CsvUploadSection from 'components/CsvUploadSection';
import GoogleSheetSection from 'components/GoogleSheetSection';

const CsvUploadPage = () => {
  const { mode: adminThemeMode } = useAdminTheme() || { mode: 'light' };
  const isDarkMode = adminThemeMode === 'dark';

  const [activeTab, setActiveTab] = useState(0);
  const [file, setFile] = useState(null);
  const [sheetUrl, setSheetUrl] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [downloadUrl, setDownloadUrl] = useState(null);
  const [data, setData] = useState(null);

  const processCsvBlob = async (blobData) => {
    const blob = new Blob([blobData], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    setDownloadUrl(url);

    const text = await blob.text();
    const lines = text.trim().split('\n');
    const headers = lines[0].split(',').map((h) => h.trim());

    const rows = lines.slice(1).map((line) => {
      const values = line.split(',').map((v) => v.trim());
      return headers.reduce((obj, header, idx) => {
        obj[header] = values[idx] ?? '';
        return obj;
      }, {});
    });

    setData(rows);
  };

  const handleUpload = async () => {
    if (!file) return;
    setIsUploading(true);
    setDownloadUrl(null);
    setData(null);

    const formData = new FormData();
    formData.append('file', file);

    try {
      const res = await getSellerBotAxiosInstance().post(
        API_ENDPOINTS.SB_MATCH_COMPANY_DATA,
        formData,
        {
          headers: { 'Content-Type': 'multipart/form-data' },
          responseType: 'blob',
        },
      );
      await processCsvBlob(res.data);
    } catch (err) {
      console.error('CSV upload failed', err);
    } finally {
      setIsUploading(false);
    }
  };

  const handleGSheetLoad = async () => {
    const match = sheetUrl.match(/\/d\/([a-zA-Z0-9-_]+)/);
    if (!match) {
      alert('Invalid sheet URL');
      return;
    }
    const sheetId = match[1];

    setIsUploading(true);
    setDownloadUrl(null);
    setData(null);

    try {
      const res = await getSellerBotAxiosInstance().post(
        API_ENDPOINTS.SB_MATCH_COMPANY_DATA,
        { sheetId },
        {
          headers: { 'Content-Type': 'application/json' },
          responseType: 'blob',
        },
      );
      await processCsvBlob(res.data);
    } catch (err) {
      console.error('GSheet processing failed', err);
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <JeffAdminPanel title="Client Management">
      <Container
        maxWidth="lg"
        sx={{
          py: 4,
          backgroundColor: isDarkMode ? '#1A202C' : '#F7FAFC',
          color: isDarkMode ? '#E2E8F0' : '#1A202C',
        }}
      >
        <Typography
          variant="h4"
          mb={2}
          color={isDarkMode ? '#90CAF9' : '#1976d2'}
          sx={{ fontWeight: 600, letterSpacing: '0.25px' }}
        >
          Client-Leads Mapping
        </Typography>
        <Typography variant="body1" mb={2}>
          Upload a CSV file with columns:{' '}
          <strong>seller_name, seller_id, client_name, sheet_name</strong>. We
          will return a processed CSV including additional columns:{' '}
          <strong>Website Status, Derived Estimated Sales</strong>.
        </Typography>

        <Tabs value={activeTab} onChange={(_, v) => setActiveTab(v)}>
          <Tab label="CSV Upload" />
          <Tab label="Google Sheet" />
        </Tabs>

        {activeTab === 0 && (
          <Box sx={{ mt: 3 }}>
            <CsvUploadSection
              file={file}
              setFile={setFile}
              handleUpload={handleUpload}
              loading={isUploading}
              isDarkMode={isDarkMode}
            />
            {downloadUrl && (
              <Box sx={{ mt: 3 }}>
                <a
                  href={downloadUrl}
                  download="processed.csv"
                  style={{ textDecoration: 'none' }}
                >
                  <Paper sx={{ p: 2, textAlign: 'center' }}>
                    <Typography variant="body2" color="primary">
                      Download Processed CSV
                    </Typography>
                  </Paper>
                </a>
              </Box>
            )}
          </Box>
        )}

        {activeTab === 1 && (
          <Box sx={{ mt: 3 }}>
            <GoogleSheetSection
              sheetUrl={sheetUrl}
              setSheetUrl={setSheetUrl}
              handleLoad={handleGSheetLoad}
              loading={isUploading}
              isDarkMode={isDarkMode}
            />
          </Box>
        )}

        {isUploading ? (
          <Box sx={{ mt: 4, textAlign: 'center' }}>
            <Typography variant="body2" sx={{ mb: 1 }}>
              Loading preview...
            </Typography>
            <CircularProgress size={28} />
          </Box>
        ) : data?.length > 0 ? (
          <Box sx={{ mt: 4 }}>
            <Typography variant="h6" mb={1}>
              Preview Data
            </Typography>
            <TableContainer
              component={Paper}
              sx={{
                maxHeight: 500,
                overflowY: 'auto',
                border: '1px solid #E2E8F0',
                backgroundColor: isDarkMode ? '#2D3748' : '#FFFFFF',
              }}
            >
              <Table stickyHeader size="small">
                <TableHead>
                  <TableRow>
                    {Object.keys(data[0] || {}).map((key) => (
                      <TableCell
                        key={key}
                        sx={{
                          backgroundColor: isDarkMode ? '#4A5568' : '#EDF2F7',
                          fontWeight: 'bold',
                          color: isDarkMode ? '#E2E8F0' : '#1A202C',
                        }}
                      >
                        {key}
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {data.map((row, i) => (
                    <TableRow key={i}>
                      {Object.values(row).map((val, j) => (
                        <TableCell
                          key={j}
                          sx={{ color: isDarkMode ? '#CBD5E0' : undefined }}
                        >
                          {val || '-'}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        ) : null}
      </Container>
    </JeffAdminPanel>
  );
};

export default CsvUploadPage;
