import React from 'react';
import {
  Box,
  Button,
  TableContainer,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Typography,
  Paper,
  Tooltip,
  CircularProgress
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';

const ClientsTable = ({ 
  clients, 
  isLoading, 
  isDarkMode 
}) => {
  return (
    <Box mt={6}>
      <Typography 
        variant="h4" 
        mb={3}
        color={isDarkMode ? '#90CAF9' : '#1976d2'}
        sx={{ 
          fontWeight: 600,
          letterSpacing: '0.25px',
          textShadow: isDarkMode ? '0px 1px 2px rgba(0,0,0,0.3)' : 'none',
        }}
      >
        All Clients
      </Typography>
      
      {isLoading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <CircularProgress sx={{ color: isDarkMode ? '#90CAF9' : '#1976d2' }} />
        </Box>
      ) : (
        <TableContainer 
          component={Paper} 
          elevation={isDarkMode ? 3 : 1}
          sx={{ 
            borderColor: 'divider',
            backgroundColor: isDarkMode ? '#1E293B' : '#ffffff',
            boxShadow: isDarkMode ? '0px 3px 15px rgba(0,0,0,0.2)' : '0px 2px 8px rgba(0,0,0,0.05)',
            overflow: 'hidden',
            borderRadius: '8px',
          }}
        >
          <Table
            sx={{ minWidth: 650 }}
            aria-label="clients table"
          >
            <TableHead>
              <TableRow sx={{ 
                bgcolor: isDarkMode ? '#2D3748' : '#f0f7ff',
                '& .MuiTableCell-root': {
                  color: isDarkMode ? '#90CAF9' : '#1976d2',
                  fontWeight: 600,
                  fontSize: '0.875rem',
                  borderBottom: isDarkMode ? '1px solid rgba(255, 255, 255, 0.12)' : '1px solid rgba(0, 0, 0, 0.12)',
                  py: 2
                }
              }}>
                <TableCell>Name</TableCell>
                <TableCell>Slug</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {clients.length > 0 ? (
                clients.map((client) => (
                  <TableRow 
                    key={client.id} 
                    hover
                    sx={{
                      '&:nth-of-type(odd)': {
                        backgroundColor: isDarkMode 
                          ? 'rgba(255, 255, 255, 0.05)' 
                          : 'rgba(0, 0, 0, 0.02)',
                      },
                      '&:hover': {
                        backgroundColor: isDarkMode
                          ? 'rgba(66, 153, 225, 0.08)'
                          : 'rgba(49, 130, 206, 0.04)',
                        cursor: 'pointer',
                      },
                      transition: 'background-color 0.2s',
                      '& .MuiTableCell-root': {
                        borderBottom: isDarkMode 
                          ? '1px solid rgba(255, 255, 255, 0.08)' 
                          : '1px solid rgba(0, 0, 0, 0.08)'
                      }
                    }}
                  >
                    <TableCell>
                      <Typography 
                        variant="body2" 
                        color={isDarkMode ? '#e2e8f0' : '#333333'}
                        fontWeight="medium"
                      >
                        {client.name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography 
                        variant="body2" 
                        color={isDarkMode ? '#e2e8f0' : '#333333'}
                      >
                        {client.slug || '-'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Tooltip title="Edit Client">
                        <Button
                          variant="contained"
                          size="small"
                          href={'/jeff/admin/client/' + client.id}
                          startIcon={<EditIcon />}
                          sx={{
                            boxShadow: isDarkMode ? '0px 2px 4px rgba(0,0,0,0.3)' : '0px 1px 2px rgba(0,0,0,0.1)',
                            backgroundColor: isDarkMode ? '#3182CE' : '#2f6ad9',
                            '&:hover': {
                              boxShadow: isDarkMode ? '0px 3px 6px rgba(0,0,0,0.4)' : '0px 2px 4px rgba(0,0,0,0.2)',
                              backgroundColor: isDarkMode ? '#4299E1' : '#3b7be8'
                            },
                            transition: 'all 0.2s ease-in-out',
                            fontWeight: 500,
                            textTransform: 'none',
                            borderRadius: '4px'
                          }}
                        >
                          Edit
                        </Button>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={3} align="center">
                    <Typography
                      variant="body2"
                      color={isDarkMode ? '#e2e8f0' : 'text.primary'}
                      sx={{ fontStyle: 'italic', py: 3 }}
                    >
                      No clients found
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Box>
  );
};

export default ClientsTable; 