'use client';

import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Box,
  Checkbox,
  FormControl,
  FormHelperText,
  Typography,
  Chip,
  IconButton,
  CircularProgress,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { useAdminTheme } from 'views/Jeff/AdminThemeContext';

const SingleLeadForm = ({
  open,
  onClose,
  form,
  onInputChange,
  onSubmit,
  isSubmitting,
  currentUrl, 
  setCurrentUrl,
  onAddUrl,
  onRemoveUrl,
  onUrlKeyPress,
}) => {
  const { mode: adminThemeMode } = useAdminTheme() || { mode: 'light' };
  const isDarkMode = adminThemeMode === 'dark';

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="md"
      PaperProps={{
        sx: {
          backgroundColor: isDarkMode ? '#1A202C' : '#FFFFFF',
          backgroundImage: 'none'
        }
      }}
    >
      <DialogTitle sx={{ 
        backgroundColor: isDarkMode ? '#2D3748' : '#F7FAFC',
        color: isDarkMode ? '#E2E8F0' : '#2D3748',
        borderBottom: isDarkMode ? '1px solid rgba(255, 255, 255, 0.1)' : '1px solid rgba(0, 0, 0, 0.1)',
        px: 3,
        py: 2
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6" component="div">
            Add Single Lead
          </Typography>
          <IconButton 
            onClick={onClose}
            size="small"
            sx={{ 
              color: isDarkMode ? '#A0AEC0' : '#718096',
              '&:hover': {
                backgroundColor: isDarkMode ? 'rgba(160, 174, 192, 0.1)' : 'rgba(113, 128, 150, 0.1)',
              }
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent sx={{ p: 3 }}>
        <Grid container spacing={2} sx={{ mt: 0 }}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Seller Name"
              name="sellerName"
              value={form.sellerName}
              onChange={onInputChange}
              placeholder="Enter seller name"
              required
              variant="outlined"
              sx={{ 
                '& .MuiOutlinedInput-root': {
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  '& fieldset': {
                    borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.23)' : 'rgba(0, 0, 0, 0.23)',
                  },
                  '&:hover fieldset': {
                    borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.4)' : 'rgba(0, 0, 0, 0.4)',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                },
                '& .MuiInputLabel-root': {
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)',
                },
              }}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Business Name"
              name="businessName"
              value={form.businessName}
              onChange={onInputChange}
              placeholder="Enter business name"
              variant="outlined"
              sx={{
                '& .MuiOutlinedInput-root': {
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  '& fieldset': {
                    borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.23)' : 'rgba(0, 0, 0, 0.23)',
                  },
                  '&:hover fieldset': {
                    borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.4)' : 'rgba(0, 0, 0, 0.4)',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                },
                '& .MuiInputLabel-root': {
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)',
                },
              }}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Address"
              name="address"
              value={form.address}
              onChange={onInputChange}
              placeholder="Enter address"
              variant="outlined"
              multiline
              rows={2}
              sx={{
                '& .MuiOutlinedInput-root': {
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  '& fieldset': {
                    borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.23)' : 'rgba(0, 0, 0, 0.23)',
                  },
                  '&:hover fieldset': {
                    borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.4)' : 'rgba(0, 0, 0, 0.4)',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                },
                '& .MuiInputLabel-root': {
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)',
                },
              }}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Industry"
              name="industry"
              value={form.industry || ''}
              onChange={onInputChange}
              placeholder="Enter industry"
              variant="outlined"
              sx={{
                '& .MuiOutlinedInput-root': {
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  '& fieldset': {
                    borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.23)' : 'rgba(0, 0, 0, 0.23)',
                  },
                  '&:hover fieldset': {
                    borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.4)' : 'rgba(0, 0, 0, 0.4)',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                },
                '& .MuiInputLabel-root': {
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)',
                },
              }}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Email"
              name="email"
              value={form.email || ''}
              onChange={onInputChange}
              placeholder="Enter email"
              variant="outlined"
              sx={{
                '& .MuiOutlinedInput-root': {
                  color: isDarkMode ? '#e2e8f0' : '#4a5568',
                  '& fieldset': {
                    borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.23)' : 'rgba(0, 0, 0, 0.23)',
                  },
                  '&:hover fieldset': {
                    borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.4)' : 'rgba(0, 0, 0, 0.4)',
                  },
                  '&.Mui-focused fieldset': {
                    borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                },
                '& .MuiInputLabel-root': {
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)',
                },
              }}
            />
          </Grid>
          <Grid item xs={12}>
            <FormControl
              sx={{
                mt: 1,
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center'
              }}
            >
              <Checkbox
                checked={form.useDomain === '1'}
                onChange={(e) => onInputChange({
                  target: {
                    name: 'useDomain',
                    value: e.target.checked ? '1' : '0'
                  }
                })}
                sx={{
                  color: isDarkMode ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.4)',
                  '&.Mui-checked': {
                    color: isDarkMode ? '#90CAF9' : '#1976d2',
                  },
                }}
              />
              <FormHelperText sx={{ color: isDarkMode ? '#E2E8F0' : '#4A5568', m: 0 }}>
                Use domain name from business website
              </FormHelperText>
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <Typography 
              variant="subtitle2" 
              sx={{ 
                color: isDarkMode ? '#E2E8F0' : '#4A5568',
                mb: 1
              }}
            >
              Business URLs
            </Typography>
            <Box sx={{ mb: 2 }}>
              <TextField
                fullWidth
                placeholder="Enter business URL and press Enter"
                value={currentUrl}
                onChange={(e) => setCurrentUrl(e.target.value)}
                onKeyPress={onUrlKeyPress}
                InputProps={{
                  endAdornment: (
                    <Button 
                      onClick={onAddUrl}
                      variant="text"
                      sx={{ 
                        color: isDarkMode ? '#90CAF9' : '#3182CE',
                        '&:hover': {
                          backgroundColor: 'transparent',
                          color: isDarkMode ? '#BEE3F8' : '#2C5282'
                        }
                      }}
                    >
                      Add
                    </Button>
                  ),
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    color: isDarkMode ? '#e2e8f0' : '#4a5568',
                    '& fieldset': {
                      borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.23)' : 'rgba(0, 0, 0, 0.23)',
                    },
                    '&:hover fieldset': {
                      borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.4)' : 'rgba(0, 0, 0, 0.4)',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: isDarkMode ? '#90CAF9' : '#1976d2',
                    },
                  },
                }}
              />
            </Box>
            
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {form.urls.map((url, index) => (
                <Chip
                  key={index}
                  label={url}
                  onDelete={() => onRemoveUrl(index)}
                  sx={{
                    backgroundColor: isDarkMode ? 'rgba(144, 202, 249, 0.1)' : 'rgba(66, 153, 225, 0.1)',
                    color: isDarkMode ? '#90CAF9' : '#3182CE',
                    '& .MuiChip-deleteIcon': {
                      color: isDarkMode ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.3)',
                      '&:hover': {
                        color: isDarkMode ? '#FFF' : '#000',
                      },
                    },
                  }}
                />
              ))}
              {form.urls.length === 0 && (
                <Typography 
                  variant="caption" 
                  sx={{ 
                    color: isDarkMode ? '#A0AEC0' : '#718096',
                    fontStyle: 'italic'
                  }}
                >
                  No URLs added yet
                </Typography>
              )}
            </Box>
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions sx={{ 
        px: 3, 
        py: 2,
        backgroundColor: isDarkMode ? '#2D3748' : '#F7FAFC',
        borderTop: isDarkMode ? '1px solid rgba(255, 255, 255, 0.1)' : '1px solid rgba(0, 0, 0, 0.1)',
      }}>
        <Button 
          onClick={onClose} 
          sx={{ 
            color: isDarkMode ? '#E2E8F0' : '#4A5568',
            '&:hover': {
              backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.05)',
            }
          }}
        >
          Cancel
        </Button>
        <Button 
          onClick={onSubmit}
          disabled={isSubmitting}
          variant="contained"
          startIcon={isSubmitting ? <CircularProgress size={20} color="inherit" /> : null}
          sx={{
            backgroundColor: isDarkMode ? '#2B6CB0' : '#3182CE',
            color: '#FFFFFF',
            '&:hover': {
              backgroundColor: isDarkMode ? '#4299E1' : '#2C5282',
            },
            '&:disabled': {
              backgroundColor: isDarkMode ? 'rgba(44, 82, 130, 0.5)' : 'rgba(49, 130, 206, 0.5)',
              color: '#FFFFFF',
            }
          }}
        >
          {isSubmitting ? 'Submitting...' : 'Submit'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SingleLeadForm; 