'use client';

import React, { useState } from 'react';
import { Icon<PERSON>utton, CircularProgress, Tooltip } from '@mui/material';
import DownloadIcon from '@mui/icons-material/Download';
import TableRowsIcon from '@mui/icons-material/TableRows';
import { getSellerBotAxiosInstance } from 'config/axios';
import { API_ENDPOINTS } from 'config/api';
import { downloadBlob } from '../utils/downloadUtils';

/**
 * Reusable download button component with local loading state
 */
const DownloadButton = ({
  jobId,
  type = 'results', // 'results' or 'input'
  isDarkMode = false,
  onError,
  fallbackHandler,
  tooltipTitle,
}) => {
  // Local loading state for this specific button
  const [isLoading, setIsLoading] = useState(false);

  // Handle the download
  const handleDownload = async () => {
    try {
      setIsLoading(true);

      // Determine the endpoint based on the download type
      let endpoint = '';
      let fileName = '';

      if (type === 'results') {
        // Use the export endpoint with a single jobId
        endpoint = API_ENDPOINTS.SB_EXPORT_LEADS.replace(':jobIds', jobId);
        fileName = `lead-export-${jobId}.zip`;
      } else if (type === 'input') {
        // Use the export input endpoint with a single jobId
        endpoint = API_ENDPOINTS.SB_EXPORT_INPUT.replace(':jobIds', jobId);
        fileName = `lead-input-${jobId}.csv`;
      }

      // Make the API request
      const response = await getSellerBotAxiosInstance().get(endpoint, {
        responseType: 'blob',
      });

      // Download the file
      downloadBlob(response.data, fileName);
    } catch (error) {
      console.error(`Error downloading ${type}:`, error);

      // Call the error handler if provided
      if (onError) {
        onError(error);
      }

      // Try fallback method if provided
      if (fallbackHandler) {
        try {
          fallbackHandler(jobId);
        } catch (fallbackError) {
          console.error('Fallback download also failed:', fallbackError);
        }
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Determine the icon to use based on the type
  const Icon = type === 'input' ? TableRowsIcon : DownloadIcon;

  // Determine the color based on the type and dark mode
  const getButtonColor = () => {
    if (type === 'input') {
      return isDarkMode ? '#9AE6B4' : '#48BB78';
    }
    return isDarkMode ? '#90CAF9' : '#4299E1';
  };

  // Determine the hover background color
  const getHoverBgColor = () => {
    if (type === 'input') {
      return isDarkMode
        ? 'rgba(154, 230, 180, 0.1)'
        : 'rgba(72, 187, 120, 0.1)';
    }
    return isDarkMode ? 'rgba(144, 202, 249, 0.1)' : 'rgba(66, 153, 225, 0.1)';
  };

  return (
    <Tooltip
      title={
        tooltipTitle ||
        (type === 'input' ? 'Export Input Data' : 'Export Results')
      }
    >
      <IconButton
        onClick={handleDownload}
        disabled={isLoading}
        size="small"
        sx={{
          color: getButtonColor(),
          '&:hover': {
            backgroundColor: getHoverBgColor(),
          },
        }}
      >
        {isLoading ? (
          <CircularProgress size={type === 'input' ? 16 : 20} color="inherit" />
        ) : (
          <Icon fontSize="small" />
        )}
      </IconButton>
    </Tooltip>
  );
};

export default DownloadButton;
