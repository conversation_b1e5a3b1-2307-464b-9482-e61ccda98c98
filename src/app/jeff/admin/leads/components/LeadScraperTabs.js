'use client';

import React, { useState } from 'react';
import {
  Box,
  Tabs,
  Tab,
  Paper,
  TextField,
  FormControl,
  FormControlLabel,
  Checkbox,
  Button,
  Typography,
  Grid,
  Chip,
  IconButton,
  RadioGroup,
  Radio,
  FormLabel
} from '@mui/material';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import AddIcon from '@mui/icons-material/Add';
import CloseIcon from '@mui/icons-material/Close';
import { useAdminTheme } from 'views/Jeff/AdminThemeContext';
import TabPanel from './TabPanel';

const LeadScraperTabs = ({
  singleLeadForm,
  onSingleLeadInputChange,
  onSingleLeadSubmit,
  isSingleLeadSubmitting,
  currentUrl,
  setCurrentUrl,
  onAddUrl,
  onRemoveUrl,
  onUrlKeyPress,
  csvFile,
  setCsvFile,
  batchMode,
  setBatchMode,
  batchUseDomain,
  setBatchUseDomain,
  onBatchLeadUpload,
  isBatchUploading
}) => {
  const { mode: adminThemeMode } = useAdminTheme() || { mode: 'light' };
  const isDarkMode = adminThemeMode === 'dark';
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleFileChange = (event) => {
    const file = event.target.files?.[0];
    if (file) {
      setCsvFile(file);
    }
  };

  return (
    <Box sx={{ width: '100%', mt: 2 }}>
      <Paper 
        sx={{ 
          backgroundColor: isDarkMode ? '#2D3748' : '#FFFFFF',
          boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
          borderRadius: '8px',
          overflow: 'hidden'
        }}
      >
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs 
            value={tabValue} 
            onChange={handleTabChange} 
            aria-label="lead scraper tabs"
            sx={{
              backgroundColor: isDarkMode ? '#1A202C' : '#F7FAFC',
              '& .MuiTab-root': {
                color: isDarkMode ? 'rgba(255,255,255,0.6)' : 'rgba(0,0,0,0.6)',
                fontWeight: 500,
                '&.Mui-selected': {
                  color: isDarkMode ? '#90CAF9' : '#1976d2',
                }
              },
              '& .MuiTabs-indicator': {
                backgroundColor: isDarkMode ? '#90CAF9' : '#1976d2',
              }
            }}
          >
            <Tab label="Single Lead Generation" id="tab-0" aria-controls="tabpanel-0" />
            <Tab label="Batch Lead Generation" id="tab-1" aria-controls="tabpanel-1" />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <Box sx={{ p: 3 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Seller Name"
                  name="sellerName"
                  value={singleLeadForm.sellerName}
                  onChange={onSingleLeadInputChange}
                  placeholder="Enter seller name"
                  required
                  variant="outlined"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Business Name"
                  name="businessName"
                  value={singleLeadForm.businessName}
                  onChange={onSingleLeadInputChange}
                  placeholder="Enter business name"
                  variant="outlined"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Seller URL"
                  name="sellerUrl"
                  value={singleLeadForm.sellerUrl}
                  onChange={onSingleLeadInputChange}
                  placeholder="Enter seller URL"
                  variant="outlined"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Address"
                  name="address"
                  value={singleLeadForm.address}
                  onChange={onSingleLeadInputChange}
                  placeholder="Enter address"
                  variant="outlined"
                  multiline
                  rows={2}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Country"
                  name="country"
                  value={singleLeadForm.country || 'US'}
                  onChange={onSingleLeadInputChange}
                  placeholder="Enter country code (e.g., US)"
                  variant="outlined"
                  helperText="Two-letter country code (default: US)"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Industry"
                  name="industry"
                  value={singleLeadForm.industry || ''}
                  onChange={onSingleLeadInputChange}
                  placeholder="Enter industry"
                  variant="outlined"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Email"
                  name="email"
                  value={singleLeadForm.email || ''}
                  onChange={onSingleLeadInputChange}
                  placeholder="Enter email"
                  variant="outlined"
                />
              </Grid>
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={singleLeadForm.useDomain === '1'}
                      onChange={(e) => onSingleLeadInputChange({
                        target: {
                          name: 'useDomain',
                          value: e.target.checked ? '1' : '0'
                        }
                      })}
                    />
                  }
                  label="Use domain name from business website"
                />
              </Grid>
              
              <Grid item xs={12} sx={{ mt: 1 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Business URLs (Optional)
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <TextField
                    fullWidth
                    placeholder="Add business URL"
                    value={currentUrl}
                    onChange={(e) => setCurrentUrl(e.target.value)}
                    onKeyPress={onUrlKeyPress}
                    variant="outlined"
                    size="small"
                  />
                  <Button 
                    startIcon={<AddIcon />}
                    onClick={onAddUrl}
                    variant="contained"
                    sx={{ ml: 1, whiteSpace: 'nowrap' }}
                  >
                    Add
                  </Button>
                </Box>
                
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {singleLeadForm.urls.map((url, index) => (
                    <Chip
                      key={index}
                      label={url}
                      onDelete={() => onRemoveUrl(index)}
                      color="primary"
                      variant="outlined"
                    />
                  ))}
                </Box>
              </Grid>
              
              <Grid item xs={12} sx={{ mt: 2, textAlign: 'right' }}>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={onSingleLeadSubmit}
                  disabled={isSingleLeadSubmitting}
                >
                  {isSingleLeadSubmitting ? 'Submitting...' : 'Generate Lead'}
                </Button>
              </Grid>
            </Grid>
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Box sx={{ p: 3 }}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormControl component="fieldset">
                  <FormLabel component="legend">Lead Generation Mode</FormLabel>
                  <RadioGroup
                    row
                    value={batchMode}
                    onChange={(e) => setBatchMode(e.target.value)}
                  >
                    <FormControlLabel value="serp" control={<Radio />} label="SERP Search (Default)" />
                    <FormControlLabel value="inputDomain" control={<Radio />} label="Use Input Domain" />
                  </RadioGroup>
                </FormControl>
              </Grid>
              
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={batchUseDomain === '1'}
                      onChange={(e) => setBatchUseDomain(e.target.checked ? '1' : '0')}
                    />
                  }
                  label="Use domain name from business website"
                />
              </Grid>
              
              <Grid item xs={12} sx={{ textAlign: 'center', border: '1px dashed rgba(0, 0, 0, 0.2)', borderRadius: '8px', p: 4 }}>
                <Box component="label" htmlFor="batch-file-upload" sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', cursor: 'pointer' }}>
                  <input
                    id="batch-file-upload"
                    type="file"
                    accept=".csv"
                    onChange={handleFileChange}
                    style={{ display: 'none' }}
                  />
                  <UploadFileIcon 
                    sx={{ 
                      fontSize: '3rem', 
                      color: isDarkMode ? '#90CAF9' : '#1976d2',
                      mb: 2,
                      opacity: 0.8
                    }} 
                  />
                  <Typography 
                    variant="h6" 

                    color={isDarkMode ? '#e2e8f0' : '#334155'} 
                    sx={{ mb: 1, fontWeight: 500 }}
                  >
                    Upload CSV with business data
                  </Typography>
                  <Typography 
                    variant="body2" 

                    color={isDarkMode ? 'rgba(226, 232, 240, 0.6)' : 'rgba(51, 65, 85, 0.6)'}
                    sx={{ mb: 2, textAlign: 'center' }}
                  >
                    CSV should include business names, addresses, and other identifying information
                  </Typography>
                  <Button
                    variant="outlined"
                    size="small"
                    component="span"
                    sx={{
                      color: isDarkMode ? '#90CAF9' : '#1976d2',
                      borderColor: isDarkMode ? 'rgba(144, 202, 249, 0.5)' : 'rgba(25, 118, 210, 0.5)',
                      fontWeight: 500,
                      textTransform: 'none'
                    }}
                  >
                    Select File
                  </Button>
                </Box>
                <Typography 
                  variant="caption" 

                  color={isDarkMode ? 'rgba(226, 232, 240, 0.6)' : 'rgba(51, 65, 85, 0.6)'} 
                  sx={{ mt: 1, display: 'block', fontStyle: 'italic', textAlign: 'center' }}
                >
                  Accepted file format: CSV (max size: 5MB)
                </Typography>
              </Grid>

              {csvFile && (
                <Grid item xs={12}>
                  <Box 
                    sx={{ 
                      p: 2,
                      borderRadius: '8px',
                      backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.01)',
                      border: isDarkMode ? '1px solid rgba(255, 255, 255, 0.05)' : '1px solid rgba(0, 0, 0, 0.05)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between'
                    }}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Box sx={{ mr: 2 }}>
                        <UploadFileIcon sx={{ color: isDarkMode ? '#90CAF9' : '#1976d2' }} />
                      </Box>
                      <Box>
                        <Typography 
                          variant="body2" 

                          sx={{ fontWeight: 500 }}
                        >
                          {csvFile.name}
                        </Typography>
                        <Typography 
                          variant="caption" 

                        >
                          {Math.round(csvFile.size / 1024)} KB
                        </Typography>
                      </Box>
                    </Box>
                    <IconButton 
                      size="small" 
                      onClick={() => setCsvFile(null)}
                      sx={{ color: isDarkMode ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.4)' }}
                    >
                      <CloseIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </Grid>
              )}
              
              <Grid item xs={12} sx={{ mt: 2, textAlign: 'right' }}>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={onBatchLeadUpload}
                  disabled={!csvFile || isBatchUploading}
                >
                  {isBatchUploading ? 'Uploading...' : 'Upload and Generate Leads'}
                </Button>
              </Grid>
            </Grid>
          </Box>
        </TabPanel>
      </Paper>
    </Box>
  );
};

export default LeadScraperTabs;