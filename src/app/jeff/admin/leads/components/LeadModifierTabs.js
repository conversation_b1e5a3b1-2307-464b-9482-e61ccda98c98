'use client';

import React, { useState } from 'react';
import {
  Box,
  Tabs,
  Tab,
  Paper,
  FormControl,
  FormControlLabel,
  Checkbox,
  Button,
  Typography,
  Grid,
  IconButton,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import UploadFileIcon from '@mui/icons-material/UploadFile';
import CloseIcon from '@mui/icons-material/Close';
import { useAdminTheme } from 'views/Jeff/AdminThemeContext';
import TabPanel from './TabPanel';

const LeadModifierTabs = ({
  setUploadType,
  uploadOperation,
  setUploadOperation,
  uploadFile,
  setUploadFile,
  skipErrorRows,
  setSkipErrorRows,
  allowBlankRows,
  setAllowBlankRows,
  onFileChange,
  onFileUpload,
  isUploading
}) => {
  const { mode: adminThemeMode } = useAdminTheme() || { mode: 'light' };
  const isDarkMode = adminThemeMode === 'dark';
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
    // Reset file on tab change
    setUploadFile(null);
    
    // Set appropriate type and operation based on tab
    if (newValue === 0) {
      setUploadType('company');
    } else if (newValue === 1) {
      setUploadType('prospect');
      setUploadOperation('insert'); // Prospect only supports insert
    } else if (newValue === 2) {
      setUploadType('matching');
      setUploadOperation('insert'); // Matching only supports insert
    }
  };

  return (
    <Box sx={{ width: '100%', mt: 2 }}>
      <Paper 
        sx={{ 
          backgroundColor: isDarkMode ? '#2D3748' : '#FFFFFF',
          boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
          borderRadius: '8px',
          overflow: 'hidden'
        }}
      >
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs 
            value={tabValue} 
            onChange={handleTabChange} 
            aria-label="lead modifier tabs"
            sx={{
              backgroundColor: isDarkMode ? '#1A202C' : '#F7FAFC',
              '& .MuiTab-root': {
                color: isDarkMode ? 'rgba(255,255,255,0.6)' : 'rgba(0,0,0,0.6)',
                fontWeight: 500,
                '&.Mui-selected': {
                  color: isDarkMode ? '#90CAF9' : '#1976d2',
                }
              },
              '& .MuiTabs-indicator': {
                backgroundColor: isDarkMode ? '#90CAF9' : '#1976d2',
              }
            }}
          >
            <Tab label="Company Data" id="tab-0" aria-controls="tabpanel-0" />
            <Tab label="Prospect Data" id="tab-1" aria-controls="tabpanel-1" />
            <Tab label="Matching Data" id="tab-2" aria-controls="tabpanel-2" />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <Box sx={{ p: 3 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth variant="outlined">
                  <InputLabel id="upload-operation-label">
                    Operation
                  </InputLabel>
                  <Select
                    labelId="upload-operation-label"
                    id="upload-operation-select"
                    value={uploadOperation}
                    onChange={(e) => setUploadOperation(e.target.value)}
                    label="Operation"
                  >
                    <MenuItem value="insert">Insert New Companies</MenuItem>
                    <MenuItem value="update">Update Existing Companies</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={skipErrorRows}
                      onChange={(e) => setSkipErrorRows(e.target.checked)}
                    />
                  }
                  label="Skip rows with errors"
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={allowBlankRows}
                      onChange={(e) => setAllowBlankRows(e.target.checked)}
                    />
                  }
                  label="Allow blank rows"
                />
              </Grid>
              
              <Grid item xs={12} sx={{ textAlign: 'center', border: '1px dashed rgba(0, 0, 0, 0.2)', borderRadius: '8px', p: 4 }}>
                <Box component="label" htmlFor="company-file-upload" sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', cursor: 'pointer' }}>
                  <input
                    id="company-file-upload"
                    type="file"
                    accept=".csv"
                    onChange={onFileChange}
                    style={{ display: 'none' }}
                  />
                  <UploadFileIcon 
                    sx={{ 
                      fontSize: '3rem', 
                      color: isDarkMode ? '#90CAF9' : '#1976d2',
                      mb: 2,
                      opacity: 0.8
                    }} 
                  />
                  <Typography 
                    variant="h6" 
                    color={isDarkMode ? '#e2e8f0' : '#334155'} 
                    sx={{ mb: 1, fontWeight: 500 }}
                  >
                    Upload Company CSV Data
                  </Typography>
                  <Typography 
                    variant="body2" 
                    color={isDarkMode ? 'rgba(226, 232, 240, 0.6)' : 'rgba(51, 65, 85, 0.6)'}
                    sx={{ mb: 2, textAlign: 'center' }}
                  >
                    Upload CSV file containing company information to {uploadOperation === 'insert' ? 'add new' : 'update existing'} company records
                  </Typography>
                  <Button
                    variant="outlined"
                    size="small"
                    component="span"
                    sx={{
                      color: isDarkMode ? '#90CAF9' : '#1976d2',
                      borderColor: isDarkMode ? 'rgba(144, 202, 249, 0.5)' : 'rgba(25, 118, 210, 0.5)',
                      fontWeight: 500,
                      textTransform: 'none'
                    }}
                  >
                    Select File
                  </Button>
                </Box>
                <Typography 
                  variant="caption" 
                  color={isDarkMode ? 'rgba(226, 232, 240, 0.6)' : 'rgba(51, 65, 85, 0.6)'} 
                  sx={{ mt: 1, display: 'block', fontStyle: 'italic', textAlign: 'center' }}
                >
                  Accepted file format: CSV (max size: 5MB)
                </Typography>
              </Grid>
            </Grid>
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Box sx={{ p: 3 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} sx={{ textAlign: 'center', border: '1px dashed rgba(0, 0, 0, 0.2)', borderRadius: '8px', p: 4 }}>
                <Box component="label" htmlFor="prospect-file-upload" sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', cursor: 'pointer' }}>
                  <input
                    id="prospect-file-upload"
                    type="file"
                    accept=".csv"
                    onChange={onFileChange}
                    style={{ display: 'none' }}
                  />
                  <UploadFileIcon 
                    sx={{ 
                      fontSize: '3rem', 
                      color: isDarkMode ? '#90CAF9' : '#1976d2',
                      mb: 2,
                      opacity: 0.8
                    }} 
                  />
                  <Typography 
                    variant="h6" 
                    color={isDarkMode ? '#e2e8f0' : '#334155'} 
                    sx={{ mb: 1, fontWeight: 500 }}
                  >
                    Upload Prospect Data
                  </Typography>
                  <Typography 
                    variant="body2" 
                    color={isDarkMode ? 'rgba(226, 232, 240, 0.6)' : 'rgba(51, 65, 85, 0.6)'}
                    sx={{ mb: 2, textAlign: 'center' }}
                  >
                    Upload CSV file containing prospect information for lead generation
                  </Typography>
                  <Button
                    variant="outlined"
                    size="small"
                    component="span"
                    sx={{
                      color: isDarkMode ? '#90CAF9' : '#1976d2',
                      borderColor: isDarkMode ? 'rgba(144, 202, 249, 0.5)' : 'rgba(25, 118, 210, 0.5)',
                      fontWeight: 500,
                      textTransform: 'none'
                    }}
                  >
                    Select File
                  </Button>
                </Box>
                <Typography 
                  variant="caption" 
                  color={isDarkMode ? 'rgba(226, 232, 240, 0.6)' : 'rgba(51, 65, 85, 0.6)'} 
                  sx={{ mt: 1, display: 'block', fontStyle: 'italic', textAlign: 'center' }}
                >
                  Accepted file format: CSV (max size: 5MB)
                </Typography>
              </Grid>
            </Grid>
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Box sx={{ p: 3 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} sx={{ textAlign: 'center', border: '1px dashed rgba(0, 0, 0, 0.2)', borderRadius: '8px', p: 4 }}>
                <Box component="label" htmlFor="matching-file-upload" sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', cursor: 'pointer' }}>
                  <input
                    id="matching-file-upload"
                    type="file"
                    accept=".csv"
                    onChange={onFileChange}
                    style={{ display: 'none' }}
                  />
                  <UploadFileIcon 
                    sx={{ 
                      fontSize: '3rem', 
                      color: isDarkMode ? '#90CAF9' : '#1976d2',
                      mb: 2,
                      opacity: 0.8
                    }} 
                  />
                  <Typography 
                    variant="h6" 
                    color={isDarkMode ? '#e2e8f0' : '#334155'} 
                    sx={{ mb: 1, fontWeight: 500 }}
                  >
                    Upload Matching Data
                  </Typography>
                  <Typography 
                    variant="body2" 
                    color={isDarkMode ? 'rgba(226, 232, 240, 0.6)' : 'rgba(51, 65, 85, 0.6)'}
                    sx={{ mb: 2, textAlign: 'center' }}
                  >
                    Upload CSV file containing matching data for lead verification
                  </Typography>
                  <Button
                    variant="outlined"
                    size="small"
                    component="span"
                    sx={{
                      color: isDarkMode ? '#90CAF9' : '#1976d2',
                      borderColor: isDarkMode ? 'rgba(144, 202, 249, 0.5)' : 'rgba(25, 118, 210, 0.5)',
                      fontWeight: 500,
                      textTransform: 'none'
                    }}
                  >
                    Select File
                  </Button>
                </Box>
                <Typography 
                  variant="caption" 
                  color={isDarkMode ? 'rgba(226, 232, 240, 0.6)' : 'rgba(51, 65, 85, 0.6)'} 
                  sx={{ mt: 1, display: 'block', fontStyle: 'italic', textAlign: 'center' }}
                >
                  Accepted file format: CSV (max size: 5MB)
                </Typography>
              </Grid>
            </Grid>
          </Box>
        </TabPanel>

        {uploadFile && (
          <Box sx={{ p: 3, pt: 0 }}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Box 
                  sx={{ 
                    p: 2,
                    borderRadius: '8px',
                    backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.02)' : 'rgba(0, 0, 0, 0.01)',
                    border: isDarkMode ? '1px solid rgba(255, 255, 255, 0.05)' : '1px solid rgba(0, 0, 0, 0.05)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between'
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box sx={{ mr: 2 }}>
                      <UploadFileIcon sx={{ color: isDarkMode ? '#90CAF9' : '#1976d2' }} />
                    </Box>
                    <Box>
                      <Typography 
                        variant="body2" 
                        color={isDarkMode ? '#e2e8f0' : '#334155'} 
                        sx={{ fontWeight: 500 }}
                      >
                        {uploadFile.name}
                      </Typography>
                      <Typography 
                        variant="caption" 
                        color={isDarkMode ? 'rgba(226, 232, 240, 0.6)' : 'rgba(51, 65, 85, 0.6)'}
                      >
                        {Math.round(uploadFile.size / 1024)} KB
                      </Typography>
                    </Box>
                  </Box>
                  <IconButton 
                    size="small" 
                    onClick={() => setUploadFile(null)}
                    sx={{ color: isDarkMode ? 'rgba(255, 255, 255, 0.6)' : 'rgba(0, 0, 0, 0.4)' }}
                  >
                    <CloseIcon fontSize="small" />
                  </IconButton>
                </Box>
              </Grid>
              
              <Grid item xs={12} sx={{ textAlign: 'right' }}>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={onFileUpload}
                  disabled={!uploadFile || isUploading}
                >
                  {isUploading ? 'Uploading...' : 'Upload File'}
                </Button>
              </Grid>
            </Grid>
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default LeadModifierTabs; 