'use client';

/**
 * Initial state and handler functions for form management
 */

// Initial state for the single lead form
export const initialSingleLeadForm = {
  sellerName: '',
  businessName: '',
  address: '',
  country: 'US',
  industry: '',
  email: '',
  useDomain: '0',
  urls: []
};

// Initial state for the lead generation form
export const initialLeadGenForm = {
  keywords: '',
  urls: []
};

/**
 * Handle input change for the single lead form
 * @param {Object} e - Event object
 * @param {Object} form - Current form state
 * @param {Function} setForm - State setter function
 */
export const handleSingleLeadInputChange = (e, setForm) => {
  const { name, value } = e.target;
  setForm(prev => ({ ...prev, [name]: value }));
};

/**
 * Handle input change for the lead generation form
 * @param {string} field - Field name
 * @param {any} value - New field value
 * @param {Object} form - Current form state
 * @param {Function} setForm - State setter function
 */
export const handleLeadGenInputChange = (field, value, form, setForm) => {
  setForm({ ...form, [field]: value });
};

/**
 * Add a URL to the form's URLs array
 * @param {string} currentUrl - URL to add
 * @param {Object} form - Current form state
 * @param {Function} setForm - State setter function
 * @param {Function} setCurrentUrl - Function to reset the current URL input
 */
export const handleAddUrl = (currentUrl, form, setForm, setCurrentUrl) => {
  if (!currentUrl) return;
  
  // Normalize URL if needed
  let url = currentUrl;
  if (!/^https?:\/\//i.test(url)) {
    url = 'https://' + url;
  }
  
  // Add to form URLs if not already present
  if (!form.urls.includes(url)) {
    setForm({ ...form, urls: [...form.urls, url] });
  }
  
  // Clear input
  setCurrentUrl('');
};

/**
 * Remove a URL from the form's URLs array
 * @param {number} index - Index of the URL to remove
 * @param {Object} form - Current form state
 * @param {Function} setForm - State setter function
 */
export const handleRemoveUrl = (index, form, setForm) => {
  const newUrls = [...form.urls];
  newUrls.splice(index, 1);
  setForm({ ...form, urls: newUrls });
};

/**
 * Handle key press events for the URL input field
 * @param {Object} e - Event object
 * @param {string} currentUrl - Current URL input value
 * @param {Object} form - Current form state
 * @param {Function} setForm - State setter function
 * @param {Function} setCurrentUrl - Function to reset the current URL input
 */
export const handleUrlKeyPress = (e, currentUrl, form, setForm, setCurrentUrl) => {
  if (e.key === 'Enter' && currentUrl) {
    e.preventDefault();
    handleAddUrl(currentUrl, form, setForm, setCurrentUrl);
  }
}; 