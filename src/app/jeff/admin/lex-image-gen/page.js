'use client';

import React, { useState, useEffect } from 'react';
import { Box, Tab, Tabs } from '@mui/material';
import JeffAdminPanel from 'views/Jeff/JeffAdminPanel';
import { useAdminTheme } from 'views/Jeff/AdminThemeContext';
import { getAxiosInstance } from 'config/axios';
import { API_ENDPOINTS } from 'config/api';
import TabPanel from '../leads/components/TabPanel';
import ImageGenJobs from './components/ImageGenJobs';
import ViolationTags from './components/ViolationTags';

export default function LexImageGenerator() {
  const [jobs, setJobs] = useState([]);
  const [selectedJobs, setSelectedJobs] = useState([]);
  const [violationTags, setViolationTags] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const { mode: adminThemeMode } = useAdminTheme();
  const isDarkMode = adminThemeMode === 'dark';
  const [tabValue, setTabValue] = useState(0);

  // Create axios instance with Jeff authentication
  const jeffAxios = getAxiosInstance({
    cookiesKey: 'jeff-authorization',
  });

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const fetchJobs = async () => {
    try {
      setLoading(true);
      const response = await jeffAxios.get(API_ENDPOINTS.LEX_IMAGE_GEN_JOBS);
      if (response.data) {
        setJobs(response.data);
      } else {
        throw new Error('No data received from server');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchViolationTags = async () => {
    try {
      setLoading(true);
      const response = await jeffAxios.get(
        API_ENDPOINTS.LEX_IMAGE_GEN_VIOLATION_TAGS,
      );
      if (response.data) {
        setViolationTags(response.data);
      } else {
        throw new Error('No data received from server');
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchJobs();
    fetchViolationTags();
  }, []);

  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const formData = new FormData();
    formData.append('csvFile', file);

    try {
      setLoading(true);
      const response = await jeffAxios.post(
        API_ENDPOINTS.LEX_IMAGE_GEN_JOBS,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        },
      );

      if (!response.data) {
        throw new Error('Failed to upload file');
      }

      await fetchJobs();
      setError(null);
    } catch (err) {
      setError(err.response?.data?.message || err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async (jobId) => {
    try {
      const downloadEndpoint = API_ENDPOINTS.LEX_IMAGE_GEN_JOB_OUTPUT.replace(
        ':id',
        jobId,
      );
      const response = await jeffAxios.get(downloadEndpoint, {
        responseType: 'blob',
      });

      const blob = new Blob([response.data]);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `lex_image_gen_${jobId}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      setError(err.response?.data?.message || err.message);
    }
  };

  const handleAddViolationTag = async (formData) => {
    try {
      setLoading(true);
      const response = await jeffAxios.post(
        API_ENDPOINTS.LEX_ADD_VIOLATION_TAG,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        },
      );

      if (!response.data) {
        throw new Error('Failed to add violation tag');
      }

      await fetchViolationTags();
      setError(null);
    } catch (err) {
      setError(err.response?.data?.message || err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateGoogleSheet = async () => {
    if (selectedJobs.length === 0) {
      setError('Please select at least one job to create a Google Sheet');
      return;
    }

    try {
      setLoading(true);
      const response = await jeffAxios.post(
        API_ENDPOINTS.GENERATE_GENERIC_GOOGLE_SHEET,
        {
          idArray: selectedJobs,
          tableName: 'lexImageGenOutputData',
          indentifierKey: 'jobId',
        },
      );

      if (!response.data) {
        throw new Error('Failed to create Google Sheet');
      }

      console.log(response.data);

      // Open the Google Sheet URL in a new tab
      window.open(response.data.sheetUrl, '_blank');
      setError(null);
    } catch (err) {
      setError(err.response?.data?.message || err.message);
    } finally {
      setLoading(false);
    }
  };

  const filteredJobs = jobs.filter((job) =>
    job.name.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  const content = (
    <Box>
      <Tabs
        value={tabValue}
        onChange={handleTabChange}
        sx={{ mb: 3 }}
        textColor="primary"
        indicatorColor="primary"
      >
        <Tab label="Image Generation Jobs" />
        <Tab label="Violation Tags" />
      </Tabs>
      <TabPanel value={tabValue} index={0}>
        <ImageGenJobs
          jobs={jobs}
          loading={loading}
          error={error}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          isDarkMode={isDarkMode}
          handleFileUpload={handleFileUpload}
          handleDownload={handleDownload}
          fetchJobs={fetchJobs}
          filteredJobs={filteredJobs}
          selectedJobs={selectedJobs}
          setSelectedJobs={setSelectedJobs}
          handleCreateGoogleSheet={handleCreateGoogleSheet}
        />
      </TabPanel>
      <TabPanel value={tabValue} index={1}>
        <ViolationTags
          tags={violationTags}
          loading={loading}
          error={error}
          isDarkMode={isDarkMode}
          handleAddTag={handleAddViolationTag}
          fetchTags={fetchViolationTags}
        />
      </TabPanel>
    </Box>
  );

  return <JeffAdminPanel title="Lex Image Generator">{content}</JeffAdminPanel>;
}
