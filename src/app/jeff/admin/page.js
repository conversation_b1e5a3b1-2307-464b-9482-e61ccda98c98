'use client';

import React, { useEffect } from 'react';
import { useUser } from 'views/Jeff/Utils/getUser';
import { useRouter } from 'next/navigation';

const AdminPage = () => {
  const { data: user, isLoading } = useUser();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading) {
      if (user && user?.userType && user.userType !== 'admin') {
        router.push('/jeff/dashboard');
      } else {
        // Redirect to clients page as default view
        router.push('/jeff/admin/client');
      }
    }
  }, [isLoading, user, router]);

  // Loading state while redirecting
  return <div>Loading...</div>;
};

export default AdminPage; 