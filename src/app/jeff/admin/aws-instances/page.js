'use client';

import React, { useEffect } from 'react';
import { Container } from '@mui/material';
import { useRouter } from 'next/navigation';
import JeffAdminPanel from 'views/Jeff/JeffAdminPanel';
import { useUser } from 'views/Jeff/Utils/getUser';
import { useAdminTheme } from 'views/Jeff/AdminThemeContext';
import InstanceSearch from './components/InstanceSearch';
import InstancesTable from './components/InstancesTable';
import Notification from 'components/Notification';
import { useAwsInstances } from './utils/hooks';

export default function AwsInstancesPage() {
  const { data: user, isLoading: isUserLoading } = useUser();
  const router = useRouter();
  const { mode: adminThemeMode } = useAdminTheme() || { mode: 'light' };
  const isDarkMode = adminThemeMode === 'dark';
  
  const {
    instances,
    isLoading,
    stats,
    searchTerm,
    setSearchTerm,
    actionLoading,
    handleInstanceAction,
    loadInstances,
    notification,
    closeNotification
  } = useAwsInstances(user);

  useEffect(() => {
    if (!isUserLoading && !user) {
      router.push('/jeff/login');
    } else if (!isUserLoading && user && user?.userType && user?.userType !== 'admin') {
      router.push('/jeff/dashboard');
    }
  }, [isUserLoading, user, router]);

  if (isUserLoading) {
    return <div>Loading...</div>;
  }

  return (
    <JeffAdminPanel title="AWS Instance Management">
      <Container maxWidth="lg" sx={{ 
        paddingY: 4,
        backgroundColor: isDarkMode ? '#1A202C' : 'inherit',
        color: isDarkMode ? '#E2E8F0' : 'inherit' 
      }}>
        <InstanceSearch
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          onRefresh={loadInstances}
          isLoading={isLoading}
          isDarkMode={isDarkMode}
        />

        <InstancesTable
          instances={instances}
          isLoading={isLoading}
          actionLoading={actionLoading}
          handleInstanceAction={handleInstanceAction}
          isDarkMode={isDarkMode}
          stats={stats}
        />

        <Notification
          open={notification.open}
          handleClose={closeNotification}
          message={notification.message}
          severity={notification.severity}
        />
      </Container>
    </JeffAdminPanel>
  );
} 