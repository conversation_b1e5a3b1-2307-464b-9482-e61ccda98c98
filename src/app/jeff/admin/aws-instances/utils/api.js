import getAxiosInstance from 'config/axios';
import { API_ENDPOINTS, getJeffBaseURL } from 'config/api';

/**
 * Fetches AWS EC2 instances
 * @returns {Promise} Promise that resolves to the AWS instances data
 */
export const fetchAwsInstances = async () => {
  try {
    const baseUrl = getJeffBaseURL();
    const response = await getAxiosInstance({
      baseUrl: baseUrl,
      cookiesKey: 'jeff-authorization',
    }).get(API_ENDPOINTS.LIST_AWS_INSTANCES);
    
    // Backend returns { success: true, data: instances }
    if (response.data && response.data.success) {
      return response.data.data || [];
    } else {
      console.warn('No instances data received or success is false:', response.data);
      throw new Error(response.data?.error || 'No instances data received');
    }
  } catch (error) {
    console.error('Error fetching AWS instances:', error);
    throw error;
  }
};

/**
 * Performs an action on an AWS instance (start, stop, reboot)
 * @param {string} instanceId - The ID of the AWS instance
 * @param {string} action - The action to perform: 'start', 'stop', or 'reboot'
 * @returns {Promise} Promise that resolves to the action result
 */
export const performInstanceAction = async (instanceId, action) => {
  try {
    const baseUrl = getJeffBaseURL();
    
    // Get the correct endpoint based on the action
    let endpoint;
    switch(action) {
      case 'start':
        endpoint = API_ENDPOINTS.START_AWS_INSTANCE.replace('id', instanceId);
        break;
      case 'stop':
        endpoint = API_ENDPOINTS.STOP_AWS_INSTANCE.replace('id', instanceId);
        break;
      case 'reboot':
        endpoint = API_ENDPOINTS.RESTART_AWS_INSTANCE.replace('id', instanceId);
        break;
      default:
        throw new Error(`Unknown action: ${action}`);
    }
    
    const response = await getAxiosInstance({
      baseUrl: baseUrl,
      cookiesKey: 'jeff-authorization', 
    }).post(endpoint);
    
    return response.data;
  } catch (error) {
    console.error(`Error with ${action} action:`, error);
    throw error;
  }
}; 