'use client';
import React from 'react';
import posthog from 'posthog-js';
import { PostHogProvider } from 'posthog-js/react';

const enableTrackingOnLocalhost = true;

if (
  typeof window !== 'undefined' &&
  !window.location.search.includes('?source=pdf') &&
  (enableTrackingOnLocalhost || !window.location.host.includes('localhost')) &&
  !window.location.pathname.includes('/jeff/audit') &&
  !window.location.pathname.includes('/redirect')
) {
  //console.log('lo', window.location);
  const PUBLIC_POSTHOG_KEY =
    process.env.NEXT_PUBLIC_POSTHOG_KEY ||
    'phc_rG7p1XQvGlKzS1Kyxf2NAdA2pvavBL6zCCSUnHqQSIW';

  const PUBLIC_POSTHOG_HOST =
    process.env.NEXT_PUBLIC_POSTHOG_HOST || 'https://us.i.posthog.com';

  posthog.init(PUBLIC_POSTHOG_KEY, {
    api_host: PUBLIC_POSTHOG_HOST,
    person_profiles: 'identified_only',
    enable_heatmaps: true,
  });
}
export function CSPostHogProvider({ children }) {
  return <PostHogProvider client={posthog}>{children}</PostHogProvider>;
}
