'use client'; // This is a client component 👈🏽

import React, { useState } from 'react';
import Avatar from '@mui/material/Avatar';
import Button from '@mui/material/Button';
import CssBaseline from '@mui/material/CssBaseline';
import TextField from '@mui/material/TextField';
// import Link from '@mui/material/Link';
// import Grid from '@mui/material/Grid';
import Box from '@mui/material/Box';
import LockOutlinedIcon from '@mui/icons-material/LockOutlined';
import Typography from '@mui/material/Typography';
import Container from '@mui/material/Container';
import { getBrandbuddyAxiosInstance } from '../../config/axios';
import { API_ENDPOINTS } from '../../config/api';
import Cookies from 'js-cookie';
import { useRouter } from 'next/navigation';

const fetchSignIn = async (payload) => {
  const response = await getBrandbuddyAxiosInstance().post(
    API_ENDPOINTS.LOGIN,
    {
      ...payload,
    },
  );
  return {
    user: response.data,
    authorization: response.headers.get('authorization'),
  };
};

const Signin = ({ redirectTo, cookiesKey }) => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const handleSubmit = async (event) => {
    event.preventDefault();
    setIsLoading(true);
    const data = new FormData(event.currentTarget);
    const payload = {
      email: data.get('email'),
      password: data.get('password'),
    };

    try {
      const { authorization } = await fetchSignIn(payload, cookiesKey);
      Cookies.set(cookiesKey, authorization);
      setIsLoading(false);
      return router.push(redirectTo);
    } catch (e) {
      console.error(e);
      setIsLoading(false);
    }
  };

  return (
    <Container component="main" maxWidth="xs">
      <CssBaseline />
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Avatar sx={{ m: 1, bgcolor: 'secondary.main' }}>
          <LockOutlinedIcon />
        </Avatar>
        <Typography component="h1" variant="h5">
          Sign in
        </Typography>
        <Box component="form" onSubmit={handleSubmit} noValidate sx={{ mt: 1 }}>
          <TextField
            margin="normal"
            required
            fullWidth
            id="email"
            label="Email Address"
            name="email"
            autoComplete="email"
            autoFocus
          />
          <TextField
            margin="normal"
            required
            fullWidth
            name="password"
            label="Password"
            type="password"
            id="password"
            autoComplete="current-password"
          />
          <Button
            type="submit"
            fullWidth
            variant="contained"
            sx={{ mt: 3, mb: 2 }}
            disabled={isLoading}
          >
            {isLoading ? 'Loading' : 'Sign In'}
          </Button>
          {/*<Grid container>*/}
          {/*  <Grid item xs></Grid>*/}
          {/*  <Grid item>*/}
          {/*    <Link href="/brandbuddy/signup" variant="body2">*/}
          {/*      {'Don\'t have an account? Sign Up'}*/}
          {/*    </Link>*/}
          {/*  </Grid>*/}
          {/*</Grid>*/}
        </Box>
      </Box>
    </Container>
  );
};

export default Signin;
