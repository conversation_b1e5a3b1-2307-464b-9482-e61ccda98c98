'use client'; // This is a client component 👈🏽

import React from 'react';
import { useTheme } from '@mui/material/styles';
import Box from '@mui/material/Box';
import EqualMain from 'layouts/EqualMain';
import Container from 'components/Container';
import {
  TimeSaved,
  CTAStrip,
  AnyChannelStrip,
  RepExamples
} from './components';
import { alpha } from '@mui/material/styles';
import { colors } from '@mui/material';
import SalesAssistantContent from '../../content/salesAssistant';
import Hero from '../../components/Equal/Hero';
import QualificationSignals from './components/QualificationSignals';
import PersonalisationWorkflow from './components/PersonalisationWorkflow';
import Spacer from '../../components/Equal/Spacer';
import CustomSolution from './components/CustomSolution';
import { Team } from 'views/PersonalisationV1/components';

const PersonalisationLanding = () => {
  const theme = useTheme();

  return (
    <EqualMain>
      <Box
        position={'relative'}
        sx={{
          backgroundColor: alpha(colors.blue[200], 0.2),
          marginTop: -13,
          paddingTop: 13,
        }}
      >
        <Container>
          <Hero
            mode={'salesAssistant'}
            mainLine={SalesAssistantContent.HERO_SECTION.mainLine}
            highlightPhraseBelowMainLine={SalesAssistantContent.HERO_SECTION.highlightPhraseBelowMainLine}
            subHero={SalesAssistantContent.HERO_SECTION.subHero}
            subHeroSub={SalesAssistantContent.HERO_SECTION.subHeroSub}
            ctaLink={SalesAssistantContent.BOOK_A_CALL_LINK}
            ctaText={SalesAssistantContent.HERO_SECTION.ctaText}
            vimeoUrl={SalesAssistantContent.HERO_SECTION.vimeoUrl}
          />
        </Container>
        <Box
          component={'svg'}
          preserveAspectRatio="none"
          xmlns="http://www.w3.org/2000/svg"
          x="0px"
          y="0px"
          viewBox="0 0 1920 100.1"
          sx={{
            width: '100%',
            marginBottom: theme.spacing(-1),
          }}
        >
          <path
            fill={theme.palette.background.paper}
            d="M0,0c0,0,934.4,93.4,1920,0v100.1H0L0,0z"
          ></path>
        </Box>
      </Box>
      <Container paddingTop={'0 !important'}>
        <PersonalisationWorkflow />
      </Container>
      <Container paddingY={4}>
        <QualificationSignals />
      </Container>
      <Box bgcolor={alpha(colors.indigo[200], 0.2)}>
        <Container paddingY={'0 !important'} paddingX={'0 !important'}>
          <CTAStrip 
            stripText={'From 1000s of signals we automate few key ones specific to your  business for efficient lead qualification.'}
            ctaLink={SalesAssistantContent.BOOK_A_CALL_LINK}
            ctaText={SalesAssistantContent.HERO_SECTION.ctaText}
          />
        </Container>
      </Box>
      <Spacer y={2} />
      <Container paddingY={1}>
        <RepExamples />
      </Container>
      <Spacer y={2} />
      <Box>
        <Container paddingY={1}>
          <CustomSolution />
        </Container>
      </Box>
      <Spacer y={2} />
      <Box bgcolor={alpha(colors.blue[200], 0.2)}>
        <Container paddingY={'0 !important'} paddingX={'0 !important'}>
          <CTAStrip 
            stripText={'AI Trained exactly like your manager would train a top sales rep'}
            ctaLink={SalesAssistantContent.BOOK_A_CALL_LINK}
            ctaText={SalesAssistantContent.HERO_SECTION.ctaText}
          />
        </Container>
      </Box>
      <Spacer y={2} />
      <Container paddingY={0}>
        <TimeSaved />
      </Container>
      <Spacer y={2} />
      <Box bgcolor={alpha(colors.blue[200], 0.2)}>
        <Container paddingY={'0 !important'} paddingX={'0 !important'}>
          <AnyChannelStrip />
        </Container>
      </Box>
      <Container paddingY={2}>
        <Team />
      </Container>
      <Box bgcolor={alpha(colors.blue[200], 0.2)}>
        <Container paddingY={'0 !important'} paddingX={'0 !important'}>
          <CTAStrip 
            stripText={'Exactly what AI promised us but $59/month products never gave.'}
            ctaLink={SalesAssistantContent.BOOK_A_CALL_LINK}
            ctaText={SalesAssistantContent.HERO_SECTION.ctaText}
          />
        </Container>
      </Box>
    </EqualMain>
  );
};

export default PersonalisationLanding;
